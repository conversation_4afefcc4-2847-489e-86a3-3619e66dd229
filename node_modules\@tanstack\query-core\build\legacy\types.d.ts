export { P as AnyDataTag, b4 as CancelOptions, T as DataTag, F as DefaultError, b3 as DefaultOptions, ai as DefaultedInfiniteQueryObserverOptions, ag as DefaultedQueryObserverOptions, aN as DefinedInfiniteQueryObserverResult, aE as DefinedQueryObserverResult, A as DistributiveOmit, Z as Enabled, al as EnsureInfiniteQueryDataOptions, ak as EnsureQueryDataOptions, am as FetchInfiniteQueryOptions, at as FetchNextPageOptions, au as FetchPreviousPageOptions, aj as FetchQueryOptions, aw as FetchStatus, a5 as GetNextPageParamFunction, a4 as GetPreviousPageParamFunction, V as InferDataFromTag, W as InferErrorFromTag, a6 as InfiniteData, aG as InfiniteQueryObserverBaseResult, aJ as InfiniteQueryObserverLoadingErrorResult, aI as InfiniteQueryObserverLoadingResult, ah as InfiniteQueryObserverOptions, aH as InfiniteQueryObserverPendingResult, aM as InfiniteQueryObserverPlaceholderResult, aK as InfiniteQueryObserverRefetchErrorResult, aO as InfiniteQueryObserverResult, aL as InfiniteQueryObserverSuccessResult, ac as InfiniteQueryPageParamsOptions, a0 as InitialDataFunction, ab as InitialPageParam, ar as InvalidateOptions, ap as InvalidateQueryFilters, aX as MutateFunction, aW as MutateOptions, aT as MutationFunction, aP as MutationKey, aS as MutationMeta, aY as MutationObserverBaseResult, a$ as MutationObserverErrorResult, aZ as MutationObserverIdleResult, a_ as MutationObserverLoadingResult, aV as MutationObserverOptions, b1 as MutationObserverResult, b0 as MutationObserverSuccessResult, aU as MutationOptions, aR as MutationScope, aQ as MutationStatus, a8 as NetworkMode, E as NoInfer, N as NonUndefinedGuard, b7 as NotifyEvent, b6 as NotifyEventType, a9 as NotifyOnChangeProps, O as OmitKeyof, B as Override, a1 as PlaceholderDataFunction, a2 as QueriesPlaceholderDataFunction, b2 as QueryClientConfig, X as QueryFunction, $ as QueryFunctionContext, G as QueryKey, a3 as QueryKeyHashFunction, a7 as QueryMeta, ax as QueryObserverBaseResult, aA as QueryObserverLoadingErrorResult, az as QueryObserverLoadingResult, ae as QueryObserverOptions, ay as QueryObserverPendingResult, aD as QueryObserverPlaceholderResult, aB as QueryObserverRefetchErrorResult, aF as QueryObserverResult, aC as QueryObserverSuccessResult, aa as QueryOptions, _ as QueryPersister, av as QueryStatus, ao as RefetchOptions, aq as RefetchQueryFilters, R as Register, as as ResetOptions, an as ResultOptions, b5 as SetDataOptions, Y as StaleTime, ad as ThrowOnError, L as UnsetMarker, af as WithRequired, J as dataTagErrorSymbol, I as dataTagSymbol, K as unsetMarker } from './hydration-17eepgNg.js';
import './removable.js';
import './subscribable.js';
