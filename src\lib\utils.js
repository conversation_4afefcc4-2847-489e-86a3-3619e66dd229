// ReportU Utility Functions

import { AUTHORITIES, OFFENSE_TYPES, COUNTRIES, EVIDENCE_TYPES } from './constants';

/**
 * Utility function to combine class names
 * @param {...string} classes - Class names to combine
 * @returns {string} Combined class names
 */
export function cn(...classes) {
  return classes.filter(Boolean).join(' ');
}

/**
 * Generate a unique ID
 * @returns {string} Unique ID
 */
export function generateId() {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Format date to readable string
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date
 */
export function formatDate(date) {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Format file size to human readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validate file type and size
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
export function validateFile(file) {
  const errors = [];
  
  // Determine file type
  let fileType = null;
  const extension = '.' + file.name.split('.').pop().toLowerCase();
  
  for (const [type, config] of Object.entries(EVIDENCE_TYPES)) {
    if (config.extensions.includes(extension)) {
      fileType = type.toLowerCase();
      break;
    }
  }
  
  if (!fileType) {
    errors.push(`Unsupported file type: ${extension}`);
  } else {
    // Check file size
    const maxSize = EVIDENCE_TYPES[fileType.toUpperCase()].maxSize;
    if (file.size > maxSize) {
      errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    fileType
  };
}

/**
 * Get appropriate authorities for offense type and country
 * @param {string} offenseType - Type of offense
 * @param {string} country - Country code
 * @returns {Array} Array of authority objects
 */
export function getAuthoritiesForOffense(offenseType, country) {
  const offense = OFFENSE_TYPES[offenseType.toUpperCase()];
  if (!offense || !offense.authorities[country]) {
    return [];
  }
  
  return offense.authorities[country].map(authorityId => 
    AUTHORITIES[authorityId.toUpperCase()]
  ).filter(Boolean);
}

/**
 * Detect country from coordinates
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @returns {string} Country code
 */
export function detectCountryFromCoordinates(lat, lng) {
  // Malaysia bounds (approximate)
  const malaysiaBounds = {
    north: 7.5,
    south: 0.8,
    east: 119.5,
    west: 99.5
  };
  
  // Singapore bounds (approximate)
  const singaporeBounds = {
    north: 1.5,
    south: 1.2,
    east: 104.1,
    west: 103.6
  };
  
  // Check Singapore first (smaller area)
  if (lat >= singaporeBounds.south && lat <= singaporeBounds.north &&
      lng >= singaporeBounds.west && lng <= singaporeBounds.east) {
    return COUNTRIES.SINGAPORE;
  }
  
  // Check Malaysia
  if (lat >= malaysiaBounds.south && lat <= malaysiaBounds.north &&
      lng >= malaysiaBounds.west && lng <= malaysiaBounds.east) {
    return COUNTRIES.MALAYSIA;
  }
  
  // Default to Malaysia if coordinates are unclear
  return COUNTRIES.MALAYSIA;
}

/**
 * Get user's current location
 * @returns {Promise<Object>} Location object with lat, lng, and country
 */
export function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;
        const country = detectCountryFromCoordinates(lat, lng);
        
        resolve({
          lat,
          lng,
          country,
          accuracy: position.coords.accuracy
        });
      },
      (error) => {
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  });
}

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Check if device is mobile
 * @returns {boolean} True if mobile device
 */
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * Copy text to clipboard
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} Success status
 */
export async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (err) {
      document.body.removeChild(textArea);
      return false;
    }
  }
}

/**
 * Generate random color
 * @returns {string} Random hex color
 */
export function getRandomColor() {
  const colors = [
    '#3b82f6', '#8b5cf6', '#06b6d4', '#10b981', 
    '#f59e0b', '#ef4444', '#ec4899', '#84cc16'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}

/**
 * Calculate distance between two coordinates
 * @param {number} lat1 - First latitude
 * @param {number} lng1 - First longitude
 * @param {number} lat2 - Second latitude
 * @param {number} lng2 - Second longitude
 * @returns {number} Distance in kilometers
 */
export function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Format address from coordinates (mock implementation)
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @returns {Promise<string>} Formatted address
 */
export async function formatAddress(lat, lng) {
  // Mock address formatting - in real app, use reverse geocoding API
  const country = detectCountryFromCoordinates(lat, lng);
  const areas = {
    [COUNTRIES.MALAYSIA]: ['Kuala Lumpur', 'Selangor', 'Penang', 'Johor', 'Perak'],
    [COUNTRIES.SINGAPORE]: ['Central', 'North', 'South', 'East', 'West']
  };
  
  const randomArea = areas[country][Math.floor(Math.random() * areas[country].length)];
  return `${randomArea}, ${country === COUNTRIES.MALAYSIA ? 'Malaysia' : 'Singapore'}`;
}

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if valid phone
 */
export function isValidPhone(phone) {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Generate report reference number
 * @param {string} country - Country code
 * @param {string} offenseType - Offense type
 * @returns {string} Report reference number
 */
export function generateReportReference(country, offenseType) {
  const countryCode = country === COUNTRIES.MALAYSIA ? 'MY' : 'SG';
  const typeCode = offenseType.substring(0, 3).toUpperCase();
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substr(2, 3).toUpperCase();
  
  return `${countryCode}-${typeCode}-${timestamp}-${random}`;
}
