{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/app/layout.js"], "sourcesContent": ["import \"./globals.css\";\n\nexport const metadata = {\n  title: \"ReportU - Cross-Border Offense Reporting Platform\",\n  description: \"Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.\",\n  keywords: \"report offense, Malaysia, Singapore, traffic violation, JPJ, LTA, PDRM, SPF, cross-border reporting\",\n  authors: [{ name: \"ReportU Team\" }],\n  creator: \"ReportU\",\n  publisher: \"ReportU\",\n  icons: {\n    icon: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    shortcut: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    apple: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n  },\n  openGraph: {\n    title: \"ReportU - Cross-Border Offense Reporting Platform\",\n    description: \"Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.\",\n    url: \"https://reportu.vercel.app\",\n    siteName: \"ReportU\",\n    images: [\n      {\n        url: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n        width: 1200,\n        height: 630,\n        alt: \"ReportU - Cross-Border Offense Reporting Platform\",\n      },\n    ],\n    locale: \"en_US\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"ReportU - Cross-Border Offense Reporting Platform\",\n    description: \"Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.\",\n    images: [\"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\"],\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <head>\n        <link rel=\"icon\" href=\"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\" />\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap\" rel=\"stylesheet\" />\n      </head>\n      <body className=\"font-inter antialiased bg-gray-950 text-white overflow-x-hidden\">\n        <div id=\"root\">\n          {children}\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAe;KAAE;IACnC,SAAS;IACT,WAAW;IACX,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA4F;IACvG;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,MAAK;wBAAkK,KAAI;;;;;;;;;;;;0BAEnL,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,IAAG;8BACL;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}