'use client';

import { useEffect } from 'react';
import Hero from '@/components/sections/Hero';
import { initializeDemoData } from '@/lib/storage';

export default function Home() {
  // Initialize demo data on first load
  useEffect(() => {
    initializeDemoData();
  }, []);

  return (
    <main className="min-h-screen">
      <Hero />

      {/* Placeholder for additional sections */}
      <div className="h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-white mb-4">More sections coming soon...</h2>
          <p className="text-gray-400">Problem/Solution, Features, Testimonials, Pricing</p>
        </div>
      </div>
    </main>
  );
}
