// ReportU Storage Utilities - Simulated Backend using LocalStorage

import { generateId, generateReportReference } from './utils';
import { REPORT_STATUS } from './constants';

// Storage Keys
const STORAGE_KEYS = {
  REPORTS: 'reportu_reports',
  USER_DATA: 'reportu_user_data',
  STATISTICS: 'reportu_statistics',
  SETTINGS: 'reportu_settings'
};

/**
 * Generic storage utilities
 */
export const storage = {
  /**
   * Get item from localStorage
   * @param {string} key - Storage key
   * @param {*} defaultValue - Default value if key doesn't exist
   * @returns {*} Stored value or default
   */
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue;
    }
  },

  /**
   * Set item in localStorage
   * @param {string} key - Storage key
   * @param {*} value - Value to store
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  },

  /**
   * Remove item from localStorage
   * @param {string} key - Storage key
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  },

  /**
   * Clear all storage
   */
  clear() {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }
};

/**
 * Report storage utilities
 */
export const reportStorage = {
  /**
   * Get all reports
   * @returns {Array} Array of reports
   */
  getAll() {
    return storage.get(STORAGE_KEYS.REPORTS, []);
  },

  /**
   * Get report by ID
   * @param {string} id - Report ID
   * @returns {Object|null} Report object or null
   */
  getById(id) {
    const reports = this.getAll();
    return reports.find(report => report.id === id) || null;
  },

  /**
   * Save new report
   * @param {Object} reportData - Report data
   * @returns {Object} Saved report with generated ID
   */
  save(reportData) {
    const reports = this.getAll();
    const newReport = {
      id: generateId(),
      reference: generateReportReference(reportData.location.country, reportData.type),
      ...reportData,
      status: REPORT_STATUS.SUBMITTED.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      timeline: [
        {
          status: REPORT_STATUS.SUBMITTED.id,
          timestamp: new Date().toISOString(),
          message: 'Report submitted successfully'
        }
      ]
    };

    reports.push(newReport);
    storage.set(STORAGE_KEYS.REPORTS, reports);
    
    // Update statistics
    this.updateStatistics();
    
    return newReport;
  },

  /**
   * Update report
   * @param {string} id - Report ID
   * @param {Object} updates - Updates to apply
   * @returns {Object|null} Updated report or null
   */
  update(id, updates) {
    const reports = this.getAll();
    const index = reports.findIndex(report => report.id === id);
    
    if (index === -1) return null;
    
    const updatedReport = {
      ...reports[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    // Add timeline entry if status changed
    if (updates.status && updates.status !== reports[index].status) {
      updatedReport.timeline = [
        ...reports[index].timeline,
        {
          status: updates.status,
          timestamp: new Date().toISOString(),
          message: updates.statusMessage || `Status changed to ${updates.status}`
        }
      ];
    }
    
    reports[index] = updatedReport;
    storage.set(STORAGE_KEYS.REPORTS, reports);
    
    return updatedReport;
  },

  /**
   * Delete report
   * @param {string} id - Report ID
   * @returns {boolean} Success status
   */
  delete(id) {
    const reports = this.getAll();
    const filteredReports = reports.filter(report => report.id !== id);
    
    if (filteredReports.length === reports.length) {
      return false; // Report not found
    }
    
    storage.set(STORAGE_KEYS.REPORTS, filteredReports);
    this.updateStatistics();
    return true;
  },

  /**
   * Get reports by filters
   * @param {Object} filters - Filter criteria
   * @returns {Array} Filtered reports
   */
  getByFilters(filters = {}) {
    const reports = this.getAll();
    
    return reports.filter(report => {
      // Filter by status
      if (filters.status && report.status !== filters.status) {
        return false;
      }
      
      // Filter by type
      if (filters.type && report.type !== filters.type) {
        return false;
      }
      
      // Filter by country
      if (filters.country && report.location.country !== filters.country) {
        return false;
      }
      
      // Filter by date range
      if (filters.startDate) {
        const reportDate = new Date(report.createdAt);
        const startDate = new Date(filters.startDate);
        if (reportDate < startDate) return false;
      }
      
      if (filters.endDate) {
        const reportDate = new Date(report.createdAt);
        const endDate = new Date(filters.endDate);
        if (reportDate > endDate) return false;
      }
      
      return true;
    });
  },

  /**
   * Update statistics
   */
  updateStatistics() {
    const reports = this.getAll();
    const stats = {
      total: reports.length,
      byStatus: {},
      byType: {},
      byCountry: {},
      lastUpdated: new Date().toISOString()
    };
    
    reports.forEach(report => {
      // Count by status
      stats.byStatus[report.status] = (stats.byStatus[report.status] || 0) + 1;
      
      // Count by type
      stats.byType[report.type] = (stats.byType[report.type] || 0) + 1;
      
      // Count by country
      stats.byCountry[report.location.country] = (stats.byCountry[report.location.country] || 0) + 1;
    });
    
    storage.set(STORAGE_KEYS.STATISTICS, stats);
  }
};

/**
 * User data storage utilities
 */
export const userStorage = {
  /**
   * Get user data
   * @returns {Object} User data
   */
  get() {
    return storage.get(STORAGE_KEYS.USER_DATA, {
      preferences: {
        language: 'en',
        notifications: true,
        anonymousReporting: false
      },
      profile: {
        name: '',
        email: '',
        phone: ''
      }
    });
  },

  /**
   * Save user data
   * @param {Object} userData - User data to save
   */
  save(userData) {
    const currentData = this.get();
    const updatedData = {
      ...currentData,
      ...userData,
      lastUpdated: new Date().toISOString()
    };
    storage.set(STORAGE_KEYS.USER_DATA, updatedData);
  },

  /**
   * Update user preferences
   * @param {Object} preferences - Preferences to update
   */
  updatePreferences(preferences) {
    const userData = this.get();
    userData.preferences = {
      ...userData.preferences,
      ...preferences
    };
    this.save(userData);
  },

  /**
   * Update user profile
   * @param {Object} profile - Profile data to update
   */
  updateProfile(profile) {
    const userData = this.get();
    userData.profile = {
      ...userData.profile,
      ...profile
    };
    this.save(userData);
  }
};

/**
 * Settings storage utilities
 */
export const settingsStorage = {
  /**
   * Get settings
   * @returns {Object} Settings
   */
  get() {
    return storage.get(STORAGE_KEYS.SETTINGS, {
      theme: 'dark',
      animations: true,
      autoLocation: true,
      notifications: true
    });
  },

  /**
   * Save settings
   * @param {Object} settings - Settings to save
   */
  save(settings) {
    const currentSettings = this.get();
    const updatedSettings = {
      ...currentSettings,
      ...settings,
      lastUpdated: new Date().toISOString()
    };
    storage.set(STORAGE_KEYS.SETTINGS, updatedSettings);
  }
};

/**
 * Statistics utilities
 */
export const statisticsStorage = {
  /**
   * Get statistics
   * @returns {Object} Statistics
   */
  get() {
    return storage.get(STORAGE_KEYS.STATISTICS, {
      total: 0,
      byStatus: {},
      byType: {},
      byCountry: {},
      lastUpdated: new Date().toISOString()
    });
  },

  /**
   * Get community statistics (mock data for demo)
   * @returns {Object} Community statistics
   */
  getCommunityStats() {
    const baseStats = this.get();
    
    // Add some mock community data for demo purposes
    return {
      ...baseStats,
      totalUsers: 12847,
      reportsThisMonth: 1234,
      resolvedThisMonth: 987,
      averageResponseTime: '2.3 hours',
      topReportTypes: [
        { type: 'traffic', count: 456, percentage: 37 },
        { type: 'public', count: 321, percentage: 26 },
        { type: 'consumer', count: 234, percentage: 19 },
        { type: 'crime', count: 123, percentage: 10 },
        { type: 'other', count: 100, percentage: 8 }
      ],
      recentActivity: [
        { id: '1', type: 'traffic', location: 'Kuala Lumpur', time: '2 minutes ago' },
        { id: '2', type: 'public', location: 'Singapore', time: '5 minutes ago' },
        { id: '3', type: 'consumer', location: 'Penang', time: '8 minutes ago' }
      ]
    };
  }
};

/**
 * Initialize demo data
 */
export function initializeDemoData() {
  const reports = reportStorage.getAll();
  
  // Only initialize if no reports exist
  if (reports.length === 0) {
    const demoReports = [
      {
        type: 'traffic',
        title: 'Illegal Parking',
        description: 'Car parked in disabled parking space without permit',
        location: {
          country: 'malaysia',
          coordinates: { lat: 3.1390, lng: 101.6869 },
          address: 'Kuala Lumpur, Malaysia'
        },
        evidence: [
          { type: 'image', url: '/demo/parking-violation.jpg', timestamp: new Date().toISOString() }
        ],
        anonymous: false,
        reporter: {
          name: 'Ahmad Rahman',
          email: '<EMAIL>'
        }
      },
      {
        type: 'public',
        title: 'Noise Complaint',
        description: 'Loud music from construction site after 10 PM',
        location: {
          country: 'singapore',
          coordinates: { lat: 1.3521, lng: 103.8198 },
          address: 'Central, Singapore'
        },
        evidence: [
          { type: 'audio', url: '/demo/noise-complaint.mp3', timestamp: new Date().toISOString() }
        ],
        anonymous: true
      }
    ];
    
    demoReports.forEach(report => reportStorage.save(report));
  }
}

export default {
  storage,
  reportStorage,
  userStorage,
  settingsStorage,
  statisticsStorage,
  initializeDemoData
};
