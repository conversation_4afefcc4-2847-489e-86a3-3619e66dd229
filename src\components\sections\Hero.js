'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { ChevronRightIcon, PlayIcon } from '@heroicons/react/24/outline';
import { gsap } from 'gsap';
import { heroAnimations } from '@/lib/animations';

export default function Hero() {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const statsRef = useRef(null);
  const [vantaEffect, setVantaEffect] = useState(null);
  const vantaRef = useRef(null);

  // Initialize Vanta.js background effect
  useEffect(() => {
    let vanta = null;
    
    const initVanta = async () => {
      if (typeof window !== 'undefined' && vantaRef.current) {
        try {
          const VANTA = (await import('vanta')).default;
          const THREE = (await import('three')).default;
          
          vanta = VANTA.BIRDS({
            el: vantaRef.current,
            THREE: THREE,
            mouseControls: true,
            touchControls: true,
            gyroControls: false,
            minHeight: 200.00,
            minWidth: 200.00,
            scale: 1.00,
            scaleMobile: 1.00,
            backgroundColor: 0x0a0a0a,
            color1: 0x3b82f6,
            color2: 0x8b5cf6,
            colorMode: 'variance',
            birdSize: 1.20,
            wingSpan: 25.00,
            speedLimit: 4.00,
            separation: 20.00,
            alignment: 20.00,
            cohesion: 20.00,
            quantity: 3.00
          });
          
          setVantaEffect(vanta);
        } catch (error) {
          console.error('Error loading Vanta.js:', error);
        }
      }
    };

    initVanta();

    return () => {
      if (vanta) vanta.destroy();
    };
  }, []);

  // Initialize GSAP animations
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const tl = gsap.timeline({ delay: 0.5 });
      
      // Set initial states
      gsap.set([titleRef.current, subtitleRef.current, ctaRef.current, statsRef.current], {
        opacity: 0,
        y: 50
      });

      // Animate elements in sequence
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power3.out'
      })
      .to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power2.out'
      }, '-=0.5')
      .to(ctaRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: 'back.out(1.7)'
      }, '-=0.3')
      .to(statsRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power2.out'
      }, '-=0.4');

      // Add floating animation to the hero section
      gsap.to(heroRef.current, {
        y: -10,
        duration: 3,
        ease: 'power1.inOut',
        repeat: -1,
        yoyo: true
      });
    }
  }, []);

  // Handle CTA button hover
  const handleCtaHover = (isHover) => {
    if (typeof window !== 'undefined') {
      gsap.to(ctaRef.current, {
        scale: isHover ? 1.05 : 1,
        y: isHover ? -2 : 0,
        duration: 0.3,
        ease: 'power2.out'
      });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Vanta.js Background */}
      <div 
        ref={vantaRef}
        className="absolute inset-0 z-0"
        style={{ width: '100%', height: '100%' }}
      />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-gray-950/50 to-gray-950 z-10" />
      
      {/* Content */}
      <div ref={heroRef} className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Main Title */}
        <h1 
          ref={titleRef}
          className="text-responsive-xl font-bold mb-6 gradient-text"
        >
          Report Offenses Across
          <br />
          <span className="text-white">Malaysia & Singapore</span>
        </h1>
        
        {/* Subtitle */}
        <p 
          ref={subtitleRef}
          className="text-responsive-md text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed"
        >
          Submit reports with multimedia evidence that are automatically routed to the appropriate authorities. 
          Track progress in real-time and contribute to safer communities.
        </p>
        
        {/* CTA Buttons */}
        <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Link 
            href="/report"
            className="btn-primary text-lg px-8 py-4 group"
            onMouseEnter={() => handleCtaHover(true)}
            onMouseLeave={() => handleCtaHover(false)}
          >
            Report Now
            <ChevronRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Link>
          
          <Link 
            href="/demo"
            className="btn-secondary text-lg px-8 py-4 group"
          >
            <PlayIcon className="w-5 h-5" />
            Watch Demo
          </Link>
        </div>
        
        {/* Statistics */}
        <div ref={statsRef} className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2">15K+</div>
            <div className="text-gray-400 text-sm">Reports Submitted</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-2">12K+</div>
            <div className="text-gray-400 text-sm">Cases Resolved</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-cyan-400 mb-2">2.3h</div>
            <div className="text-gray-400 text-sm">Avg Response</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-400 mb-2">98%</div>
            <div className="text-gray-400 text-sm">User Satisfaction</div>
          </div>
        </div>
        
        {/* Mini Demo Preview */}
        <div className="mt-16 max-w-2xl mx-auto">
          <div className="glass rounded-2xl p-6 border border-blue-500/20">
            <h3 className="text-xl font-semibold mb-4 text-center">Quick Report Preview</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">1</div>
                <div>
                  <div className="font-medium">Select Offense</div>
                  <div className="text-gray-400">Traffic, Public, Crime</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold">2</div>
                <div>
                  <div className="font-medium">Add Evidence</div>
                  <div className="text-gray-400">Photos, Videos, Audio</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold">3</div>
                <div>
                  <div className="font-medium">Track Progress</div>
                  <div className="text-gray-400">Real-time Updates</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
