import "./globals.css";

export const metadata = {
  title: "ReportU - Cross-Border Offense Reporting Platform",
  description: "Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.",
  keywords: "report offense, Malaysia, Singapore, traffic violation, JPJ, LTA, PDRM, SPF, cross-border reporting",
  authors: [{ name: "ReportU Team" }],
  creator: "ReportU",
  publisher: "ReportU",
  icons: {
    icon: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
    shortcut: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
    apple: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
  },
  openGraph: {
    title: "ReportU - Cross-Border Offense Reporting Platform",
    description: "Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.",
    url: "https://reportu.vercel.app",
    siteName: "ReportU",
    images: [
      {
        url: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
        width: 1200,
        height: 630,
        alt: "ReportU - Cross-Border Offense Reporting Platform",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "ReportU - Cross-Border Offense Reporting Platform",
    description: "Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.",
    images: ["https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap" rel="stylesheet" />
      </head>
      <body className="font-inter antialiased bg-gray-950 text-white overflow-x-hidden">
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  );
}
