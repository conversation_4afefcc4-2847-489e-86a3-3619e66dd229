{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/node_modules/vanta/src/helpers.js"], "sourcesContent": ["Number.prototype.clamp = function(min, max) { return Math.min(Math.max(this, min), max) }\r\n\r\n// # module.exports = helpers\r\n\r\nexport function mobileCheck(){\r\n  if (typeof navigator !== 'undefined') {\r\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth < 600\r\n  }\r\n  return null\r\n}\r\nexport const sample = items => items[Math.floor(Math.random()*items.length)]\r\n\r\nexport function rn(start,end) {\r\n  if (start == null) start = 0\r\n  if (end == null) end = 1\r\n  return start + (Math.random() * (end - start))\r\n}\r\n\r\nexport function ri(start,end) {\r\n  if (start == null) start = 0\r\n  if (end == null) end = 1\r\n  return Math.floor(start + (Math.random() * ((end - start) + 1)))\r\n}\r\n\r\nexport const q = sel => document.querySelector(sel)\r\n\r\nexport const color2Hex = (color) => {\r\n  if (typeof color == 'number'){\r\n    return '#' +  ('00000' + color.toString(16)).slice(-6)\r\n  } else return color\r\n}\r\n\r\nexport const color2Rgb = (color, alpha=1) => {\r\n  const hex = color2Hex(color)\r\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\r\n  const obj = result ? {\r\n      r: parseInt(result[1], 16),\r\n      g: parseInt(result[2], 16),\r\n      b: parseInt(result[3], 16)\r\n  } : null\r\n  return 'rgba('+ obj.r +','+ obj.g +','+ obj.b +','+ alpha +')'\r\n}\r\n\r\nexport const getBrightness = (threeColor) => {\r\n  return (0.299 * threeColor.r) + (0.587 * threeColor.g) + (0.114 * threeColor.b);\r\n}\r\n\r\nexport function clearThree(obj) {\r\n  // https://stackoverflow.com/questions/30359830/how-do-i-clear-three-js-scene/48722282\r\n  while (obj.children && obj.children.length > 0) {\r\n    clearThree(obj.children[0])\r\n    obj.remove(obj.children[0])\r\n  }\r\n  if (obj.geometry) obj.geometry.dispose()\r\n  if (obj.material) { // in case of map, bumpMap, normalMap, envMap ...\r\n    Object.keys(obj.material).forEach(prop => {\r\n      if (!obj.material[prop]) return\r\n      if (obj.material[prop] !== null && typeof obj.material[prop].dispose === 'function') {\r\n        obj.material[prop].dispose()\r\n      }\r\n    })\r\n    obj.material.dispose()\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG,EAAE,GAAG;IAAI,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM;AAAK;AAIjF,SAAS;IACd,IAAI,OAAO,cAAc,aAAa;QACpC,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS,KAAK,OAAO,UAAU,GAAG;IAC3H;IACA,OAAO;AACT;AACO,MAAM,SAAS,CAAA,QAAS,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAG,MAAM,MAAM,EAAE;AAErE,SAAS,GAAG,KAAK,EAAC,GAAG;IAC1B,IAAI,SAAS,MAAM,QAAQ;IAC3B,IAAI,OAAO,MAAM,MAAM;IACvB,OAAO,QAAS,KAAK,MAAM,KAAK,CAAC,MAAM,KAAK;AAC9C;AAEO,SAAS,GAAG,KAAK,EAAC,GAAG;IAC1B,IAAI,SAAS,MAAM,QAAQ;IAC3B,IAAI,OAAO,MAAM,MAAM;IACvB,OAAO,KAAK,KAAK,CAAC,QAAS,KAAK,MAAM,KAAK,CAAC,AAAC,MAAM,QAAS,CAAC;AAC/D;AAEO,MAAM,IAAI,CAAA,MAAO,SAAS,aAAa,CAAC;AAExC,MAAM,YAAY,CAAC;IACxB,IAAI,OAAO,SAAS,UAAS;QAC3B,OAAO,MAAO,CAAC,UAAU,MAAM,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtD,OAAO,OAAO;AAChB;AAEO,MAAM,YAAY,CAAC,OAAO,QAAM,CAAC;IACtC,MAAM,MAAM,UAAU;IACtB,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,MAAM,MAAM,SAAS;QACjB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IAC3B,IAAI;IACJ,OAAO,UAAS,IAAI,CAAC,GAAE,MAAK,IAAI,CAAC,GAAE,MAAK,IAAI,CAAC,GAAE,MAAK,QAAO;AAC7D;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,AAAC,QAAQ,WAAW,CAAC,GAAK,QAAQ,WAAW,CAAC,GAAK,QAAQ,WAAW,CAAC;AAChF;AAEO,SAAS,WAAW,GAAG;IAC5B,sFAAsF;IACtF,MAAO,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAG;QAC9C,WAAW,IAAI,QAAQ,CAAC,EAAE;QAC1B,IAAI,MAAM,CAAC,IAAI,QAAQ,CAAC,EAAE;IAC5B;IACA,IAAI,IAAI,QAAQ,EAAE,IAAI,QAAQ,CAAC,OAAO;IACtC,IAAI,IAAI,QAAQ,EAAE;QAChB,OAAO,IAAI,CAAC,IAAI,QAAQ,EAAE,OAAO,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,QAAQ,CAAC,KAAK,EAAE;YACzB,IAAI,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,KAAK,YAAY;gBACnF,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO;YAC5B;QACF;QACA,IAAI,QAAQ,CAAC,OAAO;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/node_modules/vanta/src/_base.js"], "sourcesContent": ["import {mobileCheck, q, color2Hex, clearThree} from './helpers.js'\r\n// const DEBUGMODE = window.location.toString().indexOf('VANTADEBUG') !== -1\r\n\r\nconst win = typeof window == 'object'\r\nlet THREE = (win && window.THREE) || {}\r\nif (win && !window.VANTA) window.VANTA = {}\r\nconst VANTA = (win && window.VANTA) || {}\r\nVANTA.register = (name, Effect) => {\r\n  return VANTA[name] = (opts) => new Effect(opts)\r\n}\r\nVANTA.version = '0.5.24'\r\n\r\nexport {VANTA}\r\n\r\n// const ORBITCONTROLS = {\r\n//   enableZoom: false,\r\n//   userPanSpeed: 3,\r\n//   userRotateSpeed: 2.0,\r\n//   maxPolarAngle: Math.PI * 0.8, // (pi/2 is pure horizontal)\r\n//   mouseButtons: {\r\n//     ORBIT: THREE.MOUSE.LEFT,\r\n//     ZOOM: null,\r\n//     PAN: null\r\n//   }\r\n// }\r\n// if (DEBUGMODE) {\r\n//   Object.assign(ORBITCONTROLS, {\r\n//     enableZoom: true,\r\n//     zoomSpeed: 4,\r\n//     minDistance: 100,\r\n//     maxDistance: 4500\r\n//   })\r\n// }\r\n\r\n// Namespace for errors\r\nconst error = function() {\r\n  Array.prototype.unshift.call(arguments, '[VANTA]')\r\n  return console.error.apply(this, arguments)\r\n}\r\n\r\nVANTA.VantaBase = class VantaBase {\r\n  constructor(userOptions = {}) {\r\n    if (!win) return false\r\n    VANTA.current = this\r\n    this.windowMouseMoveWrapper = this.windowMouseMoveWrapper.bind(this)\r\n    this.windowTouchWrapper = this.windowTouchWrapper.bind(this)\r\n    this.windowGyroWrapper = this.windowGyroWrapper.bind(this)\r\n    this.resize = this.resize.bind(this)\r\n    this.animationLoop = this.animationLoop.bind(this)\r\n    this.restart = this.restart.bind(this)\r\n\r\n    const defaultOptions = (typeof this.getDefaultOptions === 'function') ? this.getDefaultOptions() : this.defaultOptions\r\n    this.options = Object.assign({\r\n      mouseControls: true,\r\n      touchControls: true,\r\n      gyroControls: false,\r\n      minHeight: 200,\r\n      minWidth: 200,\r\n      scale: 1,\r\n      scaleMobile: 1,\r\n    }, defaultOptions)\r\n\r\n    if (userOptions instanceof HTMLElement || typeof userOptions === 'string') {\r\n      userOptions = {el: userOptions}\r\n    }\r\n    Object.assign(this.options, userOptions)\r\n\r\n    if (this.options.THREE) {\r\n      THREE = this.options.THREE // Optionally use a custom build of three.js\r\n    }\r\n\r\n    // Set element\r\n    this.el = this.options.el\r\n    if (this.el == null) {\r\n      error(\"Instance needs \\\"el\\\" param!\")\r\n    } else if (!(this.options.el instanceof HTMLElement)) {\r\n      const selector = this.el\r\n      this.el = q(selector)\r\n      if (!this.el) {\r\n        error(\"Cannot find element\", selector)\r\n        return\r\n      }\r\n    }\r\n\r\n    this.prepareEl()\r\n    this.initThree()\r\n    this.setSize() // Init needs size\r\n\r\n    try {\r\n      this.init()\r\n    } catch (e) {\r\n      // FALLBACK - just use color\r\n      error('Init error', e)\r\n      if (this.renderer && this.renderer.domElement) {\r\n        this.el.removeChild(this.renderer.domElement)\r\n      }\r\n      if (this.options.backgroundColor) {\r\n        console.log('[VANTA] Falling back to backgroundColor')\r\n        this.el.style.background = color2Hex(this.options.backgroundColor)\r\n      }\r\n      return\r\n    }\r\n\r\n    // After init\r\n    this.initMouse() // Triggers mouse, which needs to be called after init\r\n    this.resize()\r\n    this.animationLoop()\r\n\r\n    // Event listeners\r\n    const ad = window.addEventListener\r\n    ad('resize', this.resize)\r\n    window.requestAnimationFrame(this.resize) // Force a resize after the first frame\r\n\r\n    // Add event listeners on window, because this element may be below other elements, which would block the element's own mousemove event\r\n    if (this.options.mouseControls) {\r\n      ad('scroll', this.windowMouseMoveWrapper)\r\n      ad('mousemove', this.windowMouseMoveWrapper)\r\n    }\r\n    if (this.options.touchControls) {\r\n      ad('touchstart', this.windowTouchWrapper)\r\n      ad('touchmove', this.windowTouchWrapper)\r\n    }\r\n    if (this.options.gyroControls) {\r\n      ad('deviceorientation', this.windowGyroWrapper)\r\n    }\r\n  }\r\n\r\n  setOptions(userOptions={}){\r\n    Object.assign(this.options, userOptions)\r\n    this.triggerMouseMove()\r\n  }\r\n\r\n  prepareEl() {\r\n    let i, child\r\n    // wrapInner for text nodes, so text nodes can be put into foreground\r\n    if (typeof Node !== 'undefined' && Node.TEXT_NODE) {\r\n      for (i = 0; i < this.el.childNodes.length; i++) {\r\n        const n = this.el.childNodes[i]\r\n        if (n.nodeType === Node.TEXT_NODE) {\r\n          const s = document.createElement('span')\r\n          s.textContent = n.textContent\r\n          n.parentElement.insertBefore(s, n)\r\n          n.remove()\r\n        }\r\n      }\r\n    }\r\n    // Set foreground elements\r\n    for (i = 0; i < this.el.children.length; i++) {\r\n      child = this.el.children[i]\r\n      if (getComputedStyle(child).position === 'static') {\r\n        child.style.position = 'relative'\r\n      }\r\n      if (getComputedStyle(child).zIndex === 'auto') {\r\n        child.style.zIndex = 1\r\n      }\r\n    }\r\n    // Set canvas and container style\r\n    if (getComputedStyle(this.el).position === 'static') {\r\n      this.el.style.position = 'relative'\r\n    }\r\n  }\r\n\r\n  applyCanvasStyles(canvasEl, opts={}){\r\n    Object.assign(canvasEl.style, {\r\n      position: 'absolute',\r\n      zIndex: 0,\r\n      top: 0,\r\n      left: 0,\r\n      background: ''\r\n    })\r\n    Object.assign(canvasEl.style, opts)\r\n    canvasEl.classList.add('vanta-canvas')\r\n  }\r\n\r\n  initThree() {\r\n    if (!THREE.WebGLRenderer) {\r\n      console.warn(\"[VANTA] No THREE defined on window\")\r\n      return\r\n    }\r\n    // Set renderer\r\n    this.renderer = new THREE.WebGLRenderer({\r\n      alpha: true,\r\n      antialias: true\r\n    })\r\n    this.el.appendChild(this.renderer.domElement)\r\n    this.applyCanvasStyles(this.renderer.domElement)\r\n    if (isNaN(this.options.backgroundAlpha)) {\r\n      this.options.backgroundAlpha = 1\r\n    }\r\n\r\n    this.scene = new THREE.Scene()\r\n  }\r\n\r\n  getCanvasElement() {\r\n    if (this.renderer) {\r\n      return this.renderer.domElement // three.js\r\n    }\r\n    if (this.p5renderer) {\r\n      return this.p5renderer.canvas // p5\r\n    }\r\n  }\r\n\r\n  getCanvasRect() {\r\n    const canvas = this.getCanvasElement()\r\n    if (!canvas) return false\r\n    return canvas.getBoundingClientRect()\r\n  }\r\n\r\n  windowMouseMoveWrapper(e){\r\n    const rect = this.getCanvasRect()\r\n    if (!rect) return false\r\n    const x = e.clientX - rect.left\r\n    const y = e.clientY - rect.top\r\n    if (x>=0 && y>=0 && x<=rect.width && y<=rect.height) {\r\n      this.mouseX = x\r\n      this.mouseY = y\r\n      if (!this.options.mouseEase) this.triggerMouseMove(x, y)\r\n    }\r\n  }\r\n  windowTouchWrapper(e){\r\n    const rect = this.getCanvasRect()\r\n    if (!rect) return false\r\n    if (e.touches.length === 1) {\r\n      const x = e.touches[0].clientX - rect.left\r\n      const y = e.touches[0].clientY - rect.top\r\n      if (x>=0 && y>=0 && x<=rect.width && y<=rect.height) {\r\n        this.mouseX = x\r\n        this.mouseY = y\r\n        if (!this.options.mouseEase) this.triggerMouseMove(x, y)\r\n      }\r\n    }\r\n  }\r\n  windowGyroWrapper(e){\r\n    const rect = this.getCanvasRect()\r\n    if (!rect) return false\r\n    const x = Math.round(e.alpha * 2) - rect.left\r\n    const y = Math.round(e.beta * 2) - rect.top\r\n    if (x>=0 && y>=0 && x<=rect.width && y<=rect.height) {\r\n      this.mouseX = x\r\n      this.mouseY = y\r\n      if (!this.options.mouseEase) this.triggerMouseMove(x, y)\r\n    }\r\n  }\r\n\r\n  triggerMouseMove(x, y) {\r\n    if (x === undefined && y === undefined) { // trigger at current position\r\n      if (this.options.mouseEase) {\r\n        x = this.mouseEaseX\r\n        y = this.mouseEaseY\r\n      } else {\r\n        x = this.mouseX\r\n        y = this.mouseY\r\n      }\r\n    }\r\n    if (this.uniforms) {\r\n      this.uniforms.iMouse.value.x = x / this.scale // pixel values\r\n      this.uniforms.iMouse.value.y = y / this.scale // pixel values\r\n    }\r\n    const xNorm = x / this.width // 0 to 1\r\n    const yNorm = y / this.height // 0 to 1\r\n    typeof this.onMouseMove === \"function\" ? this.onMouseMove(xNorm, yNorm) : void 0\r\n  }\r\n\r\n  setSize() {\r\n    this.scale || (this.scale = 1)\r\n    if (mobileCheck() && this.options.scaleMobile) {\r\n      this.scale = this.options.scaleMobile\r\n    } else if (this.options.scale) {\r\n      this.scale = this.options.scale\r\n    }\r\n    this.width = Math.max(this.el.offsetWidth, this.options.minWidth)\r\n    this.height = Math.max(this.el.offsetHeight, this.options.minHeight)\r\n  }\r\n  initMouse() {\r\n    // Init mouseX and mouseY\r\n    if ((!this.mouseX && !this.mouseY) ||\r\n      (this.mouseX === this.options.minWidth/2 && this.mouseY === this.options.minHeight/2)) {\r\n      this.mouseX = this.width/2\r\n      this.mouseY = this.height/2\r\n      this.triggerMouseMove(this.mouseX, this.mouseY)\r\n    }\r\n  }\r\n\r\n  resize() {\r\n    this.setSize()\r\n    if (this.camera) {\r\n      this.camera.aspect = this.width / this.height\r\n      if (typeof this.camera.updateProjectionMatrix === \"function\") {\r\n        this.camera.updateProjectionMatrix()\r\n      }\r\n    }\r\n    if (this.renderer) {\r\n      this.renderer.setSize(this.width, this.height)\r\n      this.renderer.setPixelRatio(window.devicePixelRatio / this.scale)\r\n    }\r\n    typeof this.onResize === \"function\" ? this.onResize() : void 0\r\n  }\r\n\r\n  isOnScreen() {\r\n    const elHeight = this.el.offsetHeight\r\n    const elRect = this.el.getBoundingClientRect()\r\n    const scrollTop = (window.pageYOffset ||\r\n      (document.documentElement || document.body.parentNode || document.body).scrollTop\r\n    )\r\n    const offsetTop = elRect.top + scrollTop\r\n    const minScrollTop = offsetTop - window.innerHeight\r\n    const maxScrollTop = offsetTop + elHeight\r\n    return minScrollTop <= scrollTop && scrollTop <= maxScrollTop\r\n  }\r\n\r\n  animationLoop() {\r\n    // Step time\r\n    this.t || (this.t = 0)\r\n    // Uniform time\r\n    this.t2 || (this.t2 = 0)\r\n\r\n    // Normalize animation speed to 60fps\r\n    const now = performance.now()\r\n    if (this.prevNow) {\r\n      let elapsedTime = (now-this.prevNow) / (1000/60)\r\n      elapsedTime = Math.max(0.2, Math.min(elapsedTime, 5))\r\n      this.t += elapsedTime\r\n\r\n      this.t2 += (this.options.speed || 1) * elapsedTime\r\n      if (this.uniforms) {\r\n        this.uniforms.iTime.value = this.t2 * 0.016667 // iTime is in seconds\r\n      }\r\n    }\r\n    this.prevNow = now\r\n\r\n\r\n    if (this.options.mouseEase) {\r\n      this.mouseEaseX = this.mouseEaseX || this.mouseX || 0\r\n      this.mouseEaseY = this.mouseEaseY || this.mouseY || 0\r\n      if (Math.abs(this.mouseEaseX-this.mouseX) + Math.abs(this.mouseEaseY-this.mouseY) > 0.1) {\r\n        this.mouseEaseX += (this.mouseX - this.mouseEaseX) * 0.05\r\n        this.mouseEaseY += (this.mouseY - this.mouseEaseY) * 0.05\r\n        this.triggerMouseMove(this.mouseEaseX, this.mouseEaseY)\r\n      }\r\n    }\r\n\r\n    // Only animate if element is within view\r\n    if (this.isOnScreen() || this.options.forceAnimate) {\r\n      if (typeof this.onUpdate === \"function\") {\r\n        this.onUpdate()\r\n      }\r\n      if (this.scene && this.camera) {\r\n        this.renderer.render(this.scene, this.camera)\r\n        this.renderer.setClearColor(this.options.backgroundColor, this.options.backgroundAlpha)\r\n      }\r\n      // if (this.stats) this.stats.update()\r\n      // if (this.renderStats) this.renderStats.update(this.renderer)\r\n      if (this.fps && this.fps.update) this.fps.update()\r\n      if (typeof this.afterRender === \"function\") this.afterRender()\r\n    }\r\n    return this.req = window.requestAnimationFrame(this.animationLoop)\r\n  }\r\n\r\n  // setupControls() {\r\n  //   if (DEBUGMODE && THREE.OrbitControls) {\r\n  //     this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement)\r\n  //     Object.assign(this.controls, ORBITCONTROLS)\r\n  //     return this.scene.add(new THREE.AxisHelper(100))\r\n  //   }\r\n  // }\r\n\r\n  restart() {\r\n    // Restart the effect without destroying the renderer\r\n    if (this.scene) {\r\n      while (this.scene.children.length) {\r\n        this.scene.remove(this.scene.children[0])\r\n      }\r\n    }\r\n    if (typeof this.onRestart === \"function\") {\r\n      this.onRestart()\r\n    }\r\n    this.init()\r\n  }\r\n\r\n  init() {\r\n    if (typeof this.onInit === \"function\") {\r\n      this.onInit()\r\n    }\r\n    // this.setupControls()\r\n  }\r\n\r\n  destroy() {\r\n    if (typeof this.onDestroy === \"function\") {\r\n      this.onDestroy()\r\n    }\r\n    const rm = window.removeEventListener\r\n    rm('touchstart', this.windowTouchWrapper)\r\n    rm('touchmove', this.windowTouchWrapper)\r\n    rm('scroll', this.windowMouseMoveWrapper)\r\n    rm('mousemove', this.windowMouseMoveWrapper)\r\n    rm('deviceorientation', this.windowGyroWrapper)\r\n    rm('resize', this.resize)\r\n    window.cancelAnimationFrame(this.req)\r\n\r\n    const scene = this.scene\r\n    if (scene && scene.children) {\r\n      clearThree(scene)\r\n    }\r\n    if (this.renderer) {\r\n      if (this.renderer.domElement) {\r\n        this.el.removeChild(this.renderer.domElement)\r\n      }\r\n      this.renderer = null\r\n      this.scene = null\r\n    }\r\n\r\n    if (VANTA.current === this) {\r\n      VANTA.current = null\r\n    }\r\n  }\r\n}\r\n\r\nexport default VANTA.VantaBase"], "names": [], "mappings": ";;;;AAAA;;AACA,4EAA4E;AAE5E,MAAM,MAAM,OAAO,UAAU;AAC7B,IAAI,QAAQ,AAAC,OAAO,OAAO,KAAK,IAAK,CAAC;AACtC,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,OAAO,KAAK,GAAG,CAAC;AAC1C,MAAM,QAAQ,AAAC,OAAO,OAAO,KAAK,IAAK,CAAC;AACxC,MAAM,QAAQ,GAAG,CAAC,MAAM;IACtB,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,OAAS,IAAI,OAAO;AAC5C;AACA,MAAM,OAAO,GAAG;;AAIhB,0BAA0B;AAC1B,uBAAuB;AACvB,qBAAqB;AACrB,0BAA0B;AAC1B,+DAA+D;AAC/D,oBAAoB;AACpB,+BAA+B;AAC/B,kBAAkB;AAClB,gBAAgB;AAChB,MAAM;AACN,IAAI;AACJ,mBAAmB;AACnB,mCAAmC;AACnC,wBAAwB;AACxB,oBAAoB;AACpB,wBAAwB;AACxB,wBAAwB;AACxB,OAAO;AACP,IAAI;AAEJ,uBAAuB;AACvB,MAAM,QAAQ;IACZ,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;IACxC,OAAO,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;AACnC;AAEA,MAAM,SAAS,GAAG,MAAM;IACtB,YAAY,cAAc,CAAC,CAAC,CAAE;QAC5B,IAAI,CAAC,KAAK,OAAO;QACjB,MAAM,OAAO,GAAG,IAAI;QACpB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI;QACnE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;QAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI;QACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAErC,MAAM,iBAAiB,AAAC,OAAO,IAAI,CAAC,iBAAiB,KAAK,aAAc,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,cAAc;QACtH,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC;YAC3B,eAAe;YACf,eAAe;YACf,cAAc;YACd,WAAW;YACX,UAAU;YACV,OAAO;YACP,aAAa;QACf,GAAG;QAEH,IAAI,uBAAuB,eAAe,OAAO,gBAAgB,UAAU;YACzE,cAAc;gBAAC,IAAI;YAAW;QAChC;QACA,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;QAE5B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,4CAA4C;;QACzE;QAEA,cAAc;QACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM;YACnB,MAAM;QACR,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,YAAY,WAAW,GAAG;YACpD,MAAM,WAAW,IAAI,CAAC,EAAE;YACxB,IAAI,CAAC,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAC,AAAD,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACZ,MAAM,uBAAuB;gBAC7B;YACF;QACF;QAEA,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,OAAO,GAAG,kBAAkB;;QAEjC,IAAI;YACF,IAAI,CAAC,IAAI;QACX,EAAE,OAAO,GAAG;YACV,4BAA4B;YAC5B,MAAM,cAAc;YACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC7C,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;YAC9C;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAChC,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YACnE;YACA;QACF;QAEA,aAAa;QACb,IAAI,CAAC,SAAS,GAAG,sDAAsD;;QACvE,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,aAAa;QAElB,kBAAkB;QAClB,MAAM,KAAK,OAAO,gBAAgB;QAClC,GAAG,UAAU,IAAI,CAAC,MAAM;QACxB,OAAO,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,uCAAuC;;QAEjF,uIAAuI;QACvI,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC9B,GAAG,UAAU,IAAI,CAAC,sBAAsB;YACxC,GAAG,aAAa,IAAI,CAAC,sBAAsB;QAC7C;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC9B,GAAG,cAAc,IAAI,CAAC,kBAAkB;YACxC,GAAG,aAAa,IAAI,CAAC,kBAAkB;QACzC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC7B,GAAG,qBAAqB,IAAI,CAAC,iBAAiB;QAChD;IACF;IAEA,WAAW,cAAY,CAAC,CAAC,EAAC;QACxB,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;QAC5B,IAAI,CAAC,gBAAgB;IACvB;IAEA,YAAY;QACV,IAAI,GAAG;QACP,qEAAqE;QACrE,IAAI,OAAO,SAAS,eAAe,KAAK,SAAS,EAAE;YACjD,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC9C,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;gBAC/B,IAAI,EAAE,QAAQ,KAAK,KAAK,SAAS,EAAE;oBACjC,MAAM,IAAI,SAAS,aAAa,CAAC;oBACjC,EAAE,WAAW,GAAG,EAAE,WAAW;oBAC7B,EAAE,aAAa,CAAC,YAAY,CAAC,GAAG;oBAChC,EAAE,MAAM;gBACV;YACF;QACF;QACA,0BAA0B;QAC1B,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YAC5C,QAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,iBAAiB,OAAO,QAAQ,KAAK,UAAU;gBACjD,MAAM,KAAK,CAAC,QAAQ,GAAG;YACzB;YACA,IAAI,iBAAiB,OAAO,MAAM,KAAK,QAAQ;gBAC7C,MAAM,KAAK,CAAC,MAAM,GAAG;YACvB;QACF;QACA,iCAAiC;QACjC,IAAI,iBAAiB,IAAI,CAAC,EAAE,EAAE,QAAQ,KAAK,UAAU;YACnD,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG;QAC3B;IACF;IAEA,kBAAkB,QAAQ,EAAE,OAAK,CAAC,CAAC,EAAC;QAClC,OAAO,MAAM,CAAC,SAAS,KAAK,EAAE;YAC5B,UAAU;YACV,QAAQ;YACR,KAAK;YACL,MAAM;YACN,YAAY;QACd;QACA,OAAO,MAAM,CAAC,SAAS,KAAK,EAAE;QAC9B,SAAS,SAAS,CAAC,GAAG,CAAC;IACzB;IAEA,YAAY;QACV,IAAI,CAAC,MAAM,aAAa,EAAE;YACxB,QAAQ,IAAI,CAAC;YACb;QACF;QACA,eAAe;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,aAAa,CAAC;YACtC,OAAO;YACP,WAAW;QACb;QACA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;QAC5C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;QAC/C,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG;YACvC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG;QACjC;QAEA,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,KAAK;IAC9B;IAEA,mBAAmB;QACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;;QAC7C;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK;;QACrC;IACF;IAEA,gBAAgB;QACd,MAAM,SAAS,IAAI,CAAC,gBAAgB;QACpC,IAAI,CAAC,QAAQ,OAAO;QACpB,OAAO,OAAO,qBAAqB;IACrC;IAEA,uBAAuB,CAAC,EAAC;QACvB,MAAM,OAAO,IAAI,CAAC,aAAa;QAC/B,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;QAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;QAC9B,IAAI,KAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAK,IAAI,KAAG,KAAK,MAAM,EAAE;YACnD,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG;QACxD;IACF;IACA,mBAAmB,CAAC,EAAC;QACnB,MAAM,OAAO,IAAI,CAAC,aAAa;QAC/B,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,EAAE,OAAO,CAAC,MAAM,KAAK,GAAG;YAC1B,MAAM,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,IAAI;YAC1C,MAAM,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,GAAG;YACzC,IAAI,KAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAK,IAAI,KAAG,KAAK,MAAM,EAAE;gBACnD,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG;YACxD;QACF;IACF;IACA,kBAAkB,CAAC,EAAC;QAClB,MAAM,OAAO,IAAI,CAAC,aAAa;QAC/B,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,GAAG,KAAK,KAAK,IAAI;QAC7C,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,IAAI,GAAG,KAAK,KAAK,GAAG;QAC3C,IAAI,KAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAK,IAAI,KAAG,KAAK,MAAM,EAAE;YACnD,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG;QACxD;IACF;IAEA,iBAAiB,CAAC,EAAE,CAAC,EAAE;QACrB,IAAI,MAAM,aAAa,MAAM,WAAW;YACtC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,IAAI,IAAI,CAAC,UAAU;gBACnB,IAAI,IAAI,CAAC,UAAU;YACrB,OAAO;gBACL,IAAI,IAAI,CAAC,MAAM;gBACf,IAAI,IAAI,CAAC,MAAM;YACjB;QACF;QACA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe;;YAC7D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe;;QAC/D;QACA,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS;;QACtC,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;;QACvC,OAAO,IAAI,CAAC,WAAW,KAAK,aAAa,IAAI,CAAC,WAAW,CAAC,OAAO,SAAS,KAAK;IACjF;IAEA,UAAU;QACR,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC;QAC7B,IAAI,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC7C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QACvC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;QACjC;QACA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;QAChE,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;IACrE;IACA,YAAY;QACV,yBAAyB;QACzB,IAAI,AAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAC9B,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAC,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS,GAAC,GAAI;YACvF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAC;YACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;QAChD;IACF;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;YAC7C,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK,YAAY;gBAC5D,IAAI,CAAC,MAAM,CAAC,sBAAsB;YACpC;QACF;QACA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;YAC7C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,gBAAgB,GAAG,IAAI,CAAC,KAAK;QAClE;QACA,OAAO,IAAI,CAAC,QAAQ,KAAK,aAAa,IAAI,CAAC,QAAQ,KAAK,KAAK;IAC/D;IAEA,aAAa;QACX,MAAM,WAAW,IAAI,CAAC,EAAE,CAAC,YAAY;QACrC,MAAM,SAAS,IAAI,CAAC,EAAE,CAAC,qBAAqB;QAC5C,MAAM,YAAa,OAAO,WAAW,IACnC,CAAC,SAAS,eAAe,IAAI,SAAS,IAAI,CAAC,UAAU,IAAI,SAAS,IAAI,EAAE,SAAS;QAEnF,MAAM,YAAY,OAAO,GAAG,GAAG;QAC/B,MAAM,eAAe,YAAY,OAAO,WAAW;QACnD,MAAM,eAAe,YAAY;QACjC,OAAO,gBAAgB,aAAa,aAAa;IACnD;IAEA,gBAAgB;QACd,YAAY;QACZ,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QACrB,eAAe;QACf,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QAEvB,qCAAqC;QACrC,MAAM,MAAM,YAAY,GAAG;QAC3B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,cAAc,CAAC,MAAI,IAAI,CAAC,OAAO,IAAI,CAAC,OAAK,EAAE;YAC/C,cAAc,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,aAAa;YAClD,IAAI,CAAC,CAAC,IAAI;YAEV,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,IAAI;YACvC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,SAAS,sBAAsB;;YACvE;QACF;QACA,IAAI,CAAC,OAAO,GAAG;QAGf,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI;YACpD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI;YACpD,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,GAAC,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,GAAC,IAAI,CAAC,MAAM,IAAI,KAAK;gBACvF,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,IAAI;gBACrD,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,IAAI;gBACrD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU;YACxD;QACF;QAEA,yCAAyC;QACzC,IAAI,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAClD,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,YAAY;gBACvC,IAAI,CAAC,QAAQ;YACf;YACA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;gBAC5C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YACxF;YACA,sCAAsC;YACtC,+DAA+D;YAC/D,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM;YAChD,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,CAAC,WAAW;QAC9D;QACA,OAAO,IAAI,CAAC,GAAG,GAAG,OAAO,qBAAqB,CAAC,IAAI,CAAC,aAAa;IACnE;IAEA,oBAAoB;IACpB,4CAA4C;IAC5C,qFAAqF;IACrF,kDAAkD;IAClD,uDAAuD;IACvD,MAAM;IACN,IAAI;IAEJ,UAAU;QACR,qDAAqD;QACrD,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAE;gBACjC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YAC1C;QACF;QACA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,YAAY;YACxC,IAAI,CAAC,SAAS;QAChB;QACA,IAAI,CAAC,IAAI;IACX;IAEA,OAAO;QACL,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY;YACrC,IAAI,CAAC,MAAM;QACb;IACA,uBAAuB;IACzB;IAEA,UAAU;QACR,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,YAAY;YACxC,IAAI,CAAC,SAAS;QAChB;QACA,MAAM,KAAK,OAAO,mBAAmB;QACrC,GAAG,cAAc,IAAI,CAAC,kBAAkB;QACxC,GAAG,aAAa,IAAI,CAAC,kBAAkB;QACvC,GAAG,UAAU,IAAI,CAAC,sBAAsB;QACxC,GAAG,aAAa,IAAI,CAAC,sBAAsB;QAC3C,GAAG,qBAAqB,IAAI,CAAC,iBAAiB;QAC9C,GAAG,UAAU,IAAI,CAAC,MAAM;QACxB,OAAO,oBAAoB,CAAC,IAAI,CAAC,GAAG;QAEpC,MAAM,QAAQ,IAAI,CAAC,KAAK;QACxB,IAAI,SAAS,MAAM,QAAQ,EAAE;YAC3B,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE;QACb;QACA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC5B,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;YAC9C;YACA,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,KAAK,GAAG;QACf;QAEA,IAAI,MAAM,OAAO,KAAK,IAAI,EAAE;YAC1B,MAAM,OAAO,GAAG;QAClB;IACF;AACF;uCAEe,MAAM,SAAS", "ignoreList": [0], "debugId": null}}]}