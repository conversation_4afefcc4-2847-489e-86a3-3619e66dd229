(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/animations.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// ReportU Animation Utilities using GSAP
__turbopack_context__.s({
    "ANIMATION_CONFIG": (()=>ANIMATION_CONFIG),
    "animationUtils": (()=>animationUtils),
    "default": (()=>__TURBOPACK__default__export__),
    "formAnimations": (()=>formAnimations),
    "heroAnimations": (()=>heroAnimations),
    "hoverAnimations": (()=>hoverAnimations),
    "initializeGSAP": (()=>initializeGSAP),
    "loadingAnimations": (()=>loadingAnimations),
    "pageTransitions": (()=>pageTransitions),
    "scrollAnimations": (()=>scrollAnimations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/gsap/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$ScrollTrigger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/gsap/ScrollTrigger.js [app-client] (ecmascript)");
;
;
// Register GSAP plugins
if ("TURBOPACK compile-time truthy", 1) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].registerPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$ScrollTrigger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScrollTrigger"]);
}
const ANIMATION_CONFIG = {
    duration: {
        fast: 0.2,
        normal: 0.3,
        slow: 0.5,
        extraSlow: 1.0
    },
    easing: {
        power1: 'power1.out',
        power2: 'power2.out',
        power3: 'power3.out',
        back: 'back.out(1.7)',
        elastic: 'elastic.out(1, 0.3)',
        bounce: 'bounce.out'
    }
};
function initializeGSAP() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Set GSAP defaults
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].defaults({
        duration: ANIMATION_CONFIG.duration.normal,
        ease: ANIMATION_CONFIG.easing.power2
    });
    // Refresh ScrollTrigger on route changes
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$ScrollTrigger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScrollTrigger"].refresh();
}
const heroAnimations = {
    /**
   * Animate hero title entrance
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ titleEntrance (selector, options = {}) {
        const tl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline();
        tl.from(selector, {
            y: 100,
            opacity: 0,
            duration: 1,
            ease: ANIMATION_CONFIG.easing.power3,
            ...options
        });
        return tl;
    },
    /**
   * Animate hero subtitle entrance
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ subtitleEntrance (selector, options = {}) {
        const tl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline();
        tl.from(selector, {
            y: 50,
            opacity: 0,
            duration: 0.8,
            ease: ANIMATION_CONFIG.easing.power2,
            delay: 0.3,
            ...options
        });
        return tl;
    },
    /**
   * Animate CTA button entrance
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ ctaEntrance (selector, options = {}) {
        const tl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline();
        tl.from(selector, {
            scale: 0,
            opacity: 0,
            duration: 0.6,
            ease: ANIMATION_CONFIG.easing.back,
            delay: 0.6,
            ...options
        });
        return tl;
    },
    /**
   * Complete hero section entrance animation
   * @param {Object} selectors - Object containing selectors for different elements
   */ completeEntrance (selectors) {
        const tl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline();
        tl.add(this.titleEntrance(selectors.title)).add(this.subtitleEntrance(selectors.subtitle), '-=0.5').add(this.ctaEntrance(selectors.cta), '-=0.3');
        return tl;
    }
};
const scrollAnimations = {
    /**
   * Fade in from bottom on scroll
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ fadeInUp (selector, options = {}) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            y: 100,
            opacity: 0,
            duration: 0.8,
            ease: ANIMATION_CONFIG.easing.power2,
            scrollTrigger: {
                trigger: selector,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse',
                ...options.scrollTrigger
            },
            ...options
        });
    },
    /**
   * Stagger animation for multiple elements
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ staggerFadeIn (selector, options = {}) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            y: 100,
            opacity: 0,
            duration: 0.8,
            ease: ANIMATION_CONFIG.easing.power2,
            stagger: 0.2,
            scrollTrigger: {
                trigger: selector,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse',
                ...options.scrollTrigger
            },
            ...options
        });
    },
    /**
   * Scale in animation
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ scaleIn (selector, options = {}) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            scale: 0,
            opacity: 0,
            duration: 0.6,
            ease: ANIMATION_CONFIG.easing.back,
            scrollTrigger: {
                trigger: selector,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse',
                ...options.scrollTrigger
            },
            ...options
        });
    },
    /**
   * Slide in from left
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ slideInLeft (selector, options = {}) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            x: -100,
            opacity: 0,
            duration: 0.8,
            ease: ANIMATION_CONFIG.easing.power2,
            scrollTrigger: {
                trigger: selector,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse',
                ...options.scrollTrigger
            },
            ...options
        });
    },
    /**
   * Slide in from right
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */ slideInRight (selector, options = {}) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            x: 100,
            opacity: 0,
            duration: 0.8,
            ease: ANIMATION_CONFIG.easing.power2,
            scrollTrigger: {
                trigger: selector,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse',
                ...options.scrollTrigger
            },
            ...options
        });
    }
};
const hoverAnimations = {
    /**
   * Button hover effect
   * @param {string} selector - Element selector
   */ buttonHover (selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element)=>{
            const hoverTl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline({
                paused: true
            });
            hoverTl.to(element, {
                scale: 1.05,
                y: -2,
                boxShadow: '0 20px 40px rgba(59, 130, 246, 0.3)',
                duration: ANIMATION_CONFIG.duration.fast,
                ease: ANIMATION_CONFIG.easing.power2
            });
            element.addEventListener('mouseenter', ()=>hoverTl.play());
            element.addEventListener('mouseleave', ()=>hoverTl.reverse());
        });
    },
    /**
   * Card hover effect
   * @param {string} selector - Element selector
   */ cardHover (selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element)=>{
            const hoverTl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline({
                paused: true
            });
            hoverTl.to(element, {
                y: -10,
                scale: 1.02,
                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.2)',
                duration: ANIMATION_CONFIG.duration.normal,
                ease: ANIMATION_CONFIG.easing.power2
            });
            element.addEventListener('mouseenter', ()=>hoverTl.play());
            element.addEventListener('mouseleave', ()=>hoverTl.reverse());
        });
    },
    /**
   * Icon hover effect
   * @param {string} selector - Element selector
   */ iconHover (selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element)=>{
            const hoverTl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline({
                paused: true
            });
            hoverTl.to(element, {
                rotation: 360,
                scale: 1.2,
                duration: ANIMATION_CONFIG.duration.slow,
                ease: ANIMATION_CONFIG.easing.elastic
            });
            element.addEventListener('mouseenter', ()=>hoverTl.play());
            element.addEventListener('mouseleave', ()=>hoverTl.reverse());
        });
    }
};
const loadingAnimations = {
    /**
   * Pulse animation
   * @param {string} selector - Element selector
   */ pulse (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(selector, {
            scale: 1.1,
            opacity: 0.7,
            duration: 1,
            ease: 'power2.inOut',
            repeat: -1,
            yoyo: true
        });
    },
    /**
   * Spinner animation
   * @param {string} selector - Element selector
   */ spinner (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(selector, {
            rotation: 360,
            duration: 1,
            ease: 'none',
            repeat: -1
        });
    },
    /**
   * Dots loading animation
   * @param {string} selector - Element selector for dots container
   */ dots (selector) {
        const dots = document.querySelectorAll(`${selector} .dot`);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(dots, {
            y: -10,
            duration: 0.5,
            ease: 'power2.inOut',
            stagger: 0.1,
            repeat: -1,
            yoyo: true
        });
    }
};
const pageTransitions = {
    /**
   * Fade in page
   * @param {string} selector - Element selector
   */ fadeIn (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            opacity: 0,
            duration: 0.5,
            ease: ANIMATION_CONFIG.easing.power2
        });
    },
    /**
   * Slide in page from right
   * @param {string} selector - Element selector
   */ slideInFromRight (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            x: '100%',
            duration: 0.5,
            ease: ANIMATION_CONFIG.easing.power2
        });
    },
    /**
   * Scale in page
   * @param {string} selector - Element selector
   */ scaleIn (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].from(selector, {
            scale: 0.8,
            opacity: 0,
            duration: 0.5,
            ease: ANIMATION_CONFIG.easing.back
        });
    }
};
const formAnimations = {
    /**
   * Input focus animation
   * @param {string} selector - Element selector
   */ inputFocus (selector) {
        const inputs = document.querySelectorAll(selector);
        inputs.forEach((input)=>{
            const focusTl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline({
                paused: true
            });
            focusTl.to(input, {
                scale: 1.02,
                boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',
                duration: ANIMATION_CONFIG.duration.fast,
                ease: ANIMATION_CONFIG.easing.power2
            });
            input.addEventListener('focus', ()=>focusTl.play());
            input.addEventListener('blur', ()=>focusTl.reverse());
        });
    },
    /**
   * Form submission success animation
   * @param {string} selector - Element selector
   */ submitSuccess (selector) {
        const tl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline();
        tl.to(selector, {
            scale: 1.1,
            duration: 0.2,
            ease: ANIMATION_CONFIG.easing.power2
        }).to(selector, {
            scale: 1,
            duration: 0.3,
            ease: ANIMATION_CONFIG.easing.back
        });
        return tl;
    },
    /**
   * Error shake animation
   * @param {string} selector - Element selector
   */ errorShake (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(selector, {
            x: [
                -10,
                10,
                -10,
                10,
                0
            ],
            duration: 0.5,
            ease: ANIMATION_CONFIG.easing.power2
        });
    }
};
const animationUtils = {
    /**
   * Kill all animations for an element
   * @param {string} selector - Element selector
   */ killAll (selector) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].killTweensOf(selector);
    },
    /**
   * Set initial state for animations
   * @param {string} selector - Element selector
   * @param {Object} properties - CSS properties to set
   */ setInitialState (selector, properties) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].set(selector, properties);
    },
    /**
   * Refresh ScrollTrigger
   */ refreshScrollTrigger () {
        if ("TURBOPACK compile-time truthy", 1) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$ScrollTrigger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScrollTrigger"].refresh();
        }
    },
    /**
   * Create a master timeline
   * @returns {gsap.timeline} GSAP timeline
   */ createTimeline () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline();
    }
};
const __TURBOPACK__default__export__ = {
    ANIMATION_CONFIG,
    initializeGSAP,
    heroAnimations,
    scrollAnimations,
    hoverAnimations,
    loadingAnimations,
    pageTransitions,
    formAnimations,
    animationUtils
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/sections/Hero.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Hero)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChevronRightIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRightIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js [app-client] (ecmascript) <export default as ChevronRightIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PlayIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/PlayIcon.js [app-client] (ecmascript) <export default as PlayIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/gsap/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$animations$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/animations.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function Hero() {
    _s();
    const heroRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const titleRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const subtitleRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const ctaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const statsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [vantaEffect, setVantaEffect] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const vantaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Initialize Vanta.js background effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Hero.useEffect": ()=>{
            let vanta = null;
            const initVanta = {
                "Hero.useEffect.initVanta": async ()=>{
                    if ("object" !== 'undefined' && vantaRef.current) {
                        try {
                            const VANTA = (await __turbopack_context__.r("[project]/node_modules/vanta/src/_base.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)).default;
                            const THREE = (await __turbopack_context__.r("[project]/node_modules/three/build/three.module.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)).default;
                            vanta = VANTA.BIRDS({
                                el: vantaRef.current,
                                THREE: THREE,
                                mouseControls: true,
                                touchControls: true,
                                gyroControls: false,
                                minHeight: 200.00,
                                minWidth: 200.00,
                                scale: 1.00,
                                scaleMobile: 1.00,
                                backgroundColor: 0x0a0a0a,
                                color1: 0x3b82f6,
                                color2: 0x8b5cf6,
                                colorMode: 'variance',
                                birdSize: 1.20,
                                wingSpan: 25.00,
                                speedLimit: 4.00,
                                separation: 20.00,
                                alignment: 20.00,
                                cohesion: 20.00,
                                quantity: 3.00
                            });
                            setVantaEffect(vanta);
                        } catch (error) {
                            console.error('Error loading Vanta.js:', error);
                        }
                    }
                }
            }["Hero.useEffect.initVanta"];
            initVanta();
            return ({
                "Hero.useEffect": ()=>{
                    if (vanta) vanta.destroy();
                }
            })["Hero.useEffect"];
        }
    }["Hero.useEffect"], []);
    // Initialize GSAP animations
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Hero.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                const tl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].timeline({
                    delay: 0.5
                });
                // Set initial states
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].set([
                    titleRef.current,
                    subtitleRef.current,
                    ctaRef.current,
                    statsRef.current
                ], {
                    opacity: 0,
                    y: 50
                });
                // Animate elements in sequence
                tl.to(titleRef.current, {
                    opacity: 1,
                    y: 0,
                    duration: 1,
                    ease: 'power3.out'
                }).to(subtitleRef.current, {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: 'power2.out'
                }, '-=0.5').to(ctaRef.current, {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    ease: 'back.out(1.7)'
                }, '-=0.3').to(statsRef.current, {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: 'power2.out'
                }, '-=0.4');
                // Add floating animation to the hero section
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(heroRef.current, {
                    y: -10,
                    duration: 3,
                    ease: 'power1.inOut',
                    repeat: -1,
                    yoyo: true
                });
            }
        }
    }["Hero.useEffect"], []);
    // Handle CTA button hover
    const handleCtaHover = (isHover)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(ctaRef.current, {
                scale: isHover ? 1.05 : 1,
                y: isHover ? -2 : 0,
                duration: 0.3,
                ease: 'power2.out'
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: "relative min-h-screen flex items-center justify-center overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: vantaRef,
                className: "absolute inset-0 z-0",
                style: {
                    width: '100%',
                    height: '100%'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/sections/Hero.js",
                lineNumber: 128,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 bg-gradient-to-b from-transparent via-gray-950/50 to-gray-950 z-10"
            }, void 0, false, {
                fileName: "[project]/src/components/sections/Hero.js",
                lineNumber: 135,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: heroRef,
                className: "relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        ref: titleRef,
                        className: "text-responsive-xl font-bold mb-6 gradient-text",
                        children: [
                            "Report Offenses Across",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 145,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-white",
                                children: "Malaysia & Singapore"
                            }, void 0, false, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 146,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/sections/Hero.js",
                        lineNumber: 140,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        ref: subtitleRef,
                        className: "text-responsive-md text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed",
                        children: "Submit reports with multimedia evidence that are automatically routed to the appropriate authorities. Track progress in real-time and contribute to safer communities."
                    }, void 0, false, {
                        fileName: "[project]/src/components/sections/Hero.js",
                        lineNumber: 150,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: ctaRef,
                        className: "flex flex-col sm:flex-row gap-4 justify-center items-center mb-12",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/report",
                                className: "btn-primary text-lg px-8 py-4 group",
                                onMouseEnter: ()=>handleCtaHover(true),
                                onMouseLeave: ()=>handleCtaHover(false),
                                children: [
                                    "Report Now",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChevronRightIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRightIcon$3e$__["ChevronRightIcon"], {
                                        className: "w-5 h-5 group-hover:translate-x-1 transition-transform"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 167,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 160,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/demo",
                                className: "btn-secondary text-lg px-8 py-4 group",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PlayIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayIcon$3e$__["PlayIcon"], {
                                        className: "w-5 h-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 174,
                                        columnNumber: 13
                                    }, this),
                                    "Watch Demo"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/sections/Hero.js",
                        lineNumber: 159,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: statsRef,
                        className: "grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-3xl md:text-4xl font-bold text-blue-400 mb-2",
                                        children: "15K+"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 182,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-gray-400 text-sm",
                                        children: "Reports Submitted"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 183,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 181,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-3xl md:text-4xl font-bold text-purple-400 mb-2",
                                        children: "12K+"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 186,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-gray-400 text-sm",
                                        children: "Cases Resolved"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 187,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 185,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-3xl md:text-4xl font-bold text-cyan-400 mb-2",
                                        children: "2.3h"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 190,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-gray-400 text-sm",
                                        children: "Avg Response"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 191,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 189,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-3xl md:text-4xl font-bold text-green-400 mb-2",
                                        children: "98%"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 194,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-gray-400 text-sm",
                                        children: "User Satisfaction"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/sections/Hero.js",
                                        lineNumber: 195,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/sections/Hero.js",
                                lineNumber: 193,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/sections/Hero.js",
                        lineNumber: 180,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-16 max-w-2xl mx-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "glass rounded-2xl p-6 border border-blue-500/20",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-semibold mb-4 text-center",
                                    children: "Quick Report Preview"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sections/Hero.js",
                                    lineNumber: 202,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold",
                                                    children: "1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/sections/Hero.js",
                                                    lineNumber: 205,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "font-medium",
                                                            children: "Select Offense"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/sections/Hero.js",
                                                            lineNumber: 207,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-gray-400",
                                                            children: "Traffic, Public, Crime"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/sections/Hero.js",
                                                            lineNumber: 208,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/sections/Hero.js",
                                                    lineNumber: 206,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/sections/Hero.js",
                                            lineNumber: 204,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold",
                                                    children: "2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/sections/Hero.js",
                                                    lineNumber: 212,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "font-medium",
                                                            children: "Add Evidence"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/sections/Hero.js",
                                                            lineNumber: 214,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-gray-400",
                                                            children: "Photos, Videos, Audio"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/sections/Hero.js",
                                                            lineNumber: 215,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/sections/Hero.js",
                                                    lineNumber: 213,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/sections/Hero.js",
                                            lineNumber: 211,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold",
                                                    children: "3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/sections/Hero.js",
                                                    lineNumber: 219,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "font-medium",
                                                            children: "Track Progress"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/sections/Hero.js",
                                                            lineNumber: 221,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-gray-400",
                                                            children: "Real-time Updates"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/sections/Hero.js",
                                                            lineNumber: 222,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/sections/Hero.js",
                                                    lineNumber: 220,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/sections/Hero.js",
                                            lineNumber: 218,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/sections/Hero.js",
                                    lineNumber: 203,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/sections/Hero.js",
                            lineNumber: 201,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/sections/Hero.js",
                        lineNumber: 200,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/sections/Hero.js",
                lineNumber: 138,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-bounce",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-6 h-10 border-2 border-white/30 rounded-full flex justify-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/src/components/sections/Hero.js",
                            lineNumber: 234,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/sections/Hero.js",
                        lineNumber: 233,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/sections/Hero.js",
                    lineNumber: 232,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/sections/Hero.js",
                lineNumber: 231,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/sections/Hero.js",
        lineNumber: 126,
        columnNumber: 5
    }, this);
}
_s(Hero, "XCMCmPs3uRhVrPX72EwTF539zYg=");
_c = Hero;
var _c;
__turbopack_context__.k.register(_c, "Hero");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/constants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// ReportU Application Constants
// Application Information
__turbopack_context__.s({
    "ANIMATIONS": (()=>ANIMATIONS),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "APP_INFO": (()=>APP_INFO),
    "AUTHORITIES": (()=>AUTHORITIES),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "COUNTRIES": (()=>COUNTRIES),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "EVIDENCE_TYPES": (()=>EVIDENCE_TYPES),
    "FEATURES": (()=>FEATURES),
    "LANGUAGES": (()=>LANGUAGES),
    "NAV_LINKS": (()=>NAV_LINKS),
    "OFFENSE_TYPES": (()=>OFFENSE_TYPES),
    "REPORT_STATUS": (()=>REPORT_STATUS),
    "SOCIAL_LINKS": (()=>SOCIAL_LINKS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "default": (()=>__TURBOPACK__default__export__)
});
const APP_INFO = {
    name: 'ReportU',
    tagline: 'Cross-Border Offense Reporting Platform',
    description: 'Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.',
    version: '1.0.0',
    author: 'ReportU Team',
    website: 'https://reportu.vercel.app',
    logo: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp'
};
const COUNTRIES = {
    MALAYSIA: 'malaysia',
    SINGAPORE: 'singapore'
};
const AUTHORITIES = {
    // Malaysia
    JPJ: {
        id: 'jpj',
        name: 'JPJ (Road Transport Department)',
        country: COUNTRIES.MALAYSIA,
        types: [
            'traffic',
            'vehicle'
        ],
        contact: '+603-8888-1000',
        website: 'https://www.jpj.gov.my'
    },
    PDRM: {
        id: 'pdrm',
        name: 'PDRM (Royal Malaysia Police)',
        country: COUNTRIES.MALAYSIA,
        types: [
            'crime',
            'public',
            'emergency'
        ],
        contact: '999',
        website: 'https://www.rmp.gov.my'
    },
    KPDNHEP: {
        id: 'kpdnhep',
        name: 'KPDNHEP (Consumer Protection)',
        country: COUNTRIES.MALAYSIA,
        types: [
            'consumer',
            'counterfeit'
        ],
        contact: '+603-8882-5555',
        website: 'https://www.kpdnhep.gov.my'
    },
    // Singapore
    LTA: {
        id: 'lta',
        name: 'LTA (Land Transport Authority)',
        country: COUNTRIES.SINGAPORE,
        types: [
            'traffic',
            'vehicle'
        ],
        contact: '+65-6225-5582',
        website: 'https://www.lta.gov.sg'
    },
    SPF: {
        id: 'spf',
        name: 'SPF (Singapore Police Force)',
        country: COUNTRIES.SINGAPORE,
        types: [
            'crime',
            'public',
            'emergency'
        ],
        contact: '999',
        website: 'https://www.police.gov.sg'
    },
    CASE: {
        id: 'case',
        name: 'CASE (Consumer Protection)',
        country: COUNTRIES.SINGAPORE,
        types: [
            'consumer',
            'counterfeit'
        ],
        contact: '+65-6100-0315',
        website: 'https://www.case.org.sg'
    }
};
const OFFENSE_TYPES = {
    TRAFFIC: {
        id: 'traffic',
        name: 'Traffic Violations',
        icon: '🚗',
        description: 'Speeding, illegal parking, running red lights, reckless driving',
        examples: [
            'Speeding',
            'Illegal Parking',
            'Red Light Violation',
            'Reckless Driving',
            'Lane Cutting'
        ],
        authorities: {
            [COUNTRIES.MALAYSIA]: [
                'jpj',
                'pdrm'
            ],
            [COUNTRIES.SINGAPORE]: [
                'lta',
                'spf'
            ]
        }
    },
    PUBLIC: {
        id: 'public',
        name: 'Public Disturbances',
        icon: '🏛️',
        description: 'Noise complaints, littering, vandalism, public safety issues',
        examples: [
            'Noise Complaint',
            'Littering',
            'Vandalism',
            'Illegal Dumping',
            'Public Safety'
        ],
        authorities: {
            [COUNTRIES.MALAYSIA]: [
                'pdrm'
            ],
            [COUNTRIES.SINGAPORE]: [
                'spf'
            ]
        }
    },
    CONSUMER: {
        id: 'consumer',
        name: 'Consumer Protection',
        icon: '🛡️',
        description: 'Counterfeit goods, false advertising, unfair practices',
        examples: [
            'Counterfeit Products',
            'False Advertising',
            'Unfair Pricing',
            'Poor Service',
            'Scams'
        ],
        authorities: {
            [COUNTRIES.MALAYSIA]: [
                'kpdnhep'
            ],
            [COUNTRIES.SINGAPORE]: [
                'case'
            ]
        }
    },
    CRIME: {
        id: 'crime',
        name: 'Criminal Activities',
        icon: '⚖️',
        description: 'Theft, assault, fraud, drug-related offenses',
        examples: [
            'Theft',
            'Assault',
            'Fraud',
            'Drug Activity',
            'Harassment'
        ],
        authorities: {
            [COUNTRIES.MALAYSIA]: [
                'pdrm'
            ],
            [COUNTRIES.SINGAPORE]: [
                'spf'
            ]
        }
    },
    OTHER: {
        id: 'other',
        name: 'Other Offenses',
        icon: '📋',
        description: 'Other violations not covered in specific categories',
        examples: [
            'Environmental Issues',
            'Building Violations',
            'Health Code Violations'
        ],
        authorities: {
            [COUNTRIES.MALAYSIA]: [
                'pdrm'
            ],
            [COUNTRIES.SINGAPORE]: [
                'spf'
            ]
        }
    }
};
const REPORT_STATUS = {
    SUBMITTED: {
        id: 'submitted',
        name: 'Submitted',
        description: 'Report has been submitted and is being processed',
        color: '#f59e0b',
        icon: '📝'
    },
    PROCESSING: {
        id: 'processing',
        name: 'Processing',
        description: 'Report is being reviewed by the appropriate authority',
        color: '#3b82f6',
        icon: '⏳'
    },
    INVESTIGATING: {
        id: 'investigating',
        name: 'Investigating',
        description: 'Authority is actively investigating the reported offense',
        color: '#8b5cf6',
        icon: '🔍'
    },
    RESOLVED: {
        id: 'resolved',
        name: 'Resolved',
        description: 'Report has been resolved and action has been taken',
        color: '#10b981',
        icon: '✅'
    },
    CLOSED: {
        id: 'closed',
        name: 'Closed',
        description: 'Report has been closed (no action required or insufficient evidence)',
        color: '#6b7280',
        icon: '❌'
    }
};
const EVIDENCE_TYPES = {
    IMAGE: {
        id: 'image',
        name: 'Photo',
        extensions: [
            '.jpg',
            '.jpeg',
            '.png',
            '.webp'
        ],
        maxSize: 10 * 1024 * 1024,
        icon: '📸'
    },
    VIDEO: {
        id: 'video',
        name: 'Video',
        extensions: [
            '.mp4',
            '.mov',
            '.avi',
            '.webm'
        ],
        maxSize: 50 * 1024 * 1024,
        icon: '🎥'
    },
    AUDIO: {
        id: 'audio',
        name: 'Audio',
        extensions: [
            '.mp3',
            '.wav',
            '.m4a',
            '.ogg'
        ],
        maxSize: 20 * 1024 * 1024,
        icon: '🎵'
    }
};
const LANGUAGES = {
    EN: {
        code: 'en',
        name: 'English',
        flag: '🇺🇸'
    },
    MS: {
        code: 'ms',
        name: 'Bahasa Malaysia',
        flag: '🇲🇾'
    },
    ZH: {
        code: 'zh',
        name: '中文',
        flag: '🇨🇳'
    },
    TA: {
        code: 'ta',
        name: 'தமிழ்',
        flag: '🇮🇳'
    }
};
const NAV_LINKS = [
    {
        href: '/',
        label: 'Home',
        icon: '🏠'
    },
    {
        href: '/demo',
        label: 'Demo',
        icon: '🎮'
    },
    {
        href: '/report',
        label: 'Report',
        icon: '📝'
    },
    {
        href: '/dashboard',
        label: 'Dashboard',
        icon: '📊'
    },
    {
        href: '/about',
        label: 'About',
        icon: 'ℹ️'
    }
];
const SOCIAL_LINKS = {
    twitter: 'https://twitter.com/reportu',
    facebook: 'https://facebook.com/reportu',
    linkedin: 'https://linkedin.com/company/reportu',
    github: 'https://github.com/reportu'
};
const API_ENDPOINTS = {
    REPORTS: '/api/reports',
    UPLOAD: '/api/upload',
    AUTHORITIES: '/api/authorities',
    STATISTICS: '/api/statistics'
};
const ANIMATIONS = {
    DURATION: {
        FAST: 0.2,
        NORMAL: 0.3,
        SLOW: 0.5,
        EXTRA_SLOW: 1.0
    },
    EASING: {
        EASE_OUT: 'ease-out',
        EASE_IN_OUT: 'ease-in-out',
        BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
};
const BREAKPOINTS = {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536
};
const FEATURES = {
    ANONYMOUS_REPORTING: true,
    MULTI_LANGUAGE: true,
    REAL_TIME_TRACKING: true,
    COMMUNITY_DASHBOARD: true,
    CROSS_BORDER: true,
    AI_CATEGORIZATION: true
};
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your connection and try again.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
    INVALID_FILE_TYPE: 'Invalid file type. Please select a supported file.',
    LOCATION_ERROR: 'Unable to detect your location. Please enable location services.',
    SUBMISSION_ERROR: 'Failed to submit report. Please try again later.'
};
const SUCCESS_MESSAGES = {
    REPORT_SUBMITTED: 'Report submitted successfully! You will receive updates on the progress.',
    FILE_UPLOADED: 'File uploaded successfully.',
    LOCATION_DETECTED: 'Location detected successfully.'
};
const __TURBOPACK__default__export__ = {
    APP_INFO,
    COUNTRIES,
    AUTHORITIES,
    OFFENSE_TYPES,
    REPORT_STATUS,
    EVIDENCE_TYPES,
    LANGUAGES,
    NAV_LINKS,
    SOCIAL_LINKS,
    API_ENDPOINTS,
    ANIMATIONS,
    BREAKPOINTS,
    FEATURES,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// ReportU Utility Functions
__turbopack_context__.s({
    "calculateDistance": (()=>calculateDistance),
    "cn": (()=>cn),
    "copyToClipboard": (()=>copyToClipboard),
    "debounce": (()=>debounce),
    "detectCountryFromCoordinates": (()=>detectCountryFromCoordinates),
    "formatAddress": (()=>formatAddress),
    "formatDate": (()=>formatDate),
    "formatFileSize": (()=>formatFileSize),
    "generateId": (()=>generateId),
    "generateReportReference": (()=>generateReportReference),
    "getAuthoritiesForOffense": (()=>getAuthoritiesForOffense),
    "getCurrentLocation": (()=>getCurrentLocation),
    "getRandomColor": (()=>getRandomColor),
    "isMobile": (()=>isMobile),
    "isValidEmail": (()=>isValidEmail),
    "isValidPhone": (()=>isValidPhone),
    "throttle": (()=>throttle),
    "validateFile": (()=>validateFile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.js [app-client] (ecmascript)");
;
function cn(...classes) {
    return classes.filter(Boolean).join(' ');
}
function generateId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
function formatDate(date) {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function validateFile(file) {
    const errors = [];
    // Determine file type
    let fileType = null;
    const extension = '.' + file.name.split('.').pop().toLowerCase();
    for (const [type, config] of Object.entries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVIDENCE_TYPES"])){
        if (config.extensions.includes(extension)) {
            fileType = type.toLowerCase();
            break;
        }
    }
    if (!fileType) {
        errors.push(`Unsupported file type: ${extension}`);
    } else {
        // Check file size
        const maxSize = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVIDENCE_TYPES"][fileType.toUpperCase()].maxSize;
        if (file.size > maxSize) {
            errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`);
        }
    }
    return {
        isValid: errors.length === 0,
        errors,
        fileType
    };
}
function getAuthoritiesForOffense(offenseType, country) {
    const offense = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OFFENSE_TYPES"][offenseType.toUpperCase()];
    if (!offense || !offense.authorities[country]) {
        return [];
    }
    return offense.authorities[country].map((authorityId)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AUTHORITIES"][authorityId.toUpperCase()]).filter(Boolean);
}
function detectCountryFromCoordinates(lat, lng) {
    // Malaysia bounds (approximate)
    const malaysiaBounds = {
        north: 7.5,
        south: 0.8,
        east: 119.5,
        west: 99.5
    };
    // Singapore bounds (approximate)
    const singaporeBounds = {
        north: 1.5,
        south: 1.2,
        east: 104.1,
        west: 103.6
    };
    // Check Singapore first (smaller area)
    if (lat >= singaporeBounds.south && lat <= singaporeBounds.north && lng >= singaporeBounds.west && lng <= singaporeBounds.east) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].SINGAPORE;
    }
    // Check Malaysia
    if (lat >= malaysiaBounds.south && lat <= malaysiaBounds.north && lng >= malaysiaBounds.west && lng <= malaysiaBounds.east) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].MALAYSIA;
    }
    // Default to Malaysia if coordinates are unclear
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].MALAYSIA;
}
function getCurrentLocation() {
    return new Promise((resolve, reject)=>{
        if (!navigator.geolocation) {
            reject(new Error('Geolocation is not supported by this browser'));
            return;
        }
        navigator.geolocation.getCurrentPosition((position)=>{
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const country = detectCountryFromCoordinates(lat, lng);
            resolve({
                lat,
                lng,
                country,
                accuracy: position.coords.accuracy
            });
        }, (error)=>{
            reject(error);
        }, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
        });
    });
}
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = ()=>{
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        } catch (err) {
            document.body.removeChild(textArea);
            return false;
        }
    }
}
function getRandomColor() {
    const colors = [
        '#3b82f6',
        '#8b5cf6',
        '#06b6d4',
        '#10b981',
        '#f59e0b',
        '#ef4444',
        '#ec4899',
        '#84cc16'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
}
async function formatAddress(lat, lng) {
    // Mock address formatting - in real app, use reverse geocoding API
    const country = detectCountryFromCoordinates(lat, lng);
    const areas = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].MALAYSIA]: [
            'Kuala Lumpur',
            'Selangor',
            'Penang',
            'Johor',
            'Perak'
        ],
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].SINGAPORE]: [
            'Central',
            'North',
            'South',
            'East',
            'West'
        ]
    };
    const randomArea = areas[country][Math.floor(Math.random() * areas[country].length)];
    return `${randomArea}, ${country === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].MALAYSIA ? 'Malaysia' : 'Singapore'}`;
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}
function generateReportReference(country, offenseType) {
    const countryCode = country === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COUNTRIES"].MALAYSIA ? 'MY' : 'SG';
    const typeCode = offenseType.substring(0, 3).toUpperCase();
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 3).toUpperCase();
    return `${countryCode}-${typeCode}-${timestamp}-${random}`;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/storage.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// ReportU Storage Utilities - Simulated Backend using LocalStorage
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "initializeDemoData": (()=>initializeDemoData),
    "reportStorage": (()=>reportStorage),
    "settingsStorage": (()=>settingsStorage),
    "statisticsStorage": (()=>statisticsStorage),
    "storage": (()=>storage),
    "userStorage": (()=>userStorage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.js [app-client] (ecmascript)");
;
;
// Storage Keys
const STORAGE_KEYS = {
    REPORTS: 'reportu_reports',
    USER_DATA: 'reportu_user_data',
    STATISTICS: 'reportu_statistics',
    SETTINGS: 'reportu_settings'
};
const storage = {
    /**
   * Get item from localStorage
   * @param {string} key - Storage key
   * @param {*} defaultValue - Default value if key doesn't exist
   * @returns {*} Stored value or default
   */ get (key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    },
    /**
   * Set item in localStorage
   * @param {string} key - Storage key
   * @param {*} value - Value to store
   */ set (key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error writing to localStorage:', error);
        }
    },
    /**
   * Remove item from localStorage
   * @param {string} key - Storage key
   */ remove (key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error removing from localStorage:', error);
        }
    },
    /**
   * Clear all storage
   */ clear () {
        try {
            Object.values(STORAGE_KEYS).forEach((key)=>{
                localStorage.removeItem(key);
            });
        } catch (error) {
            console.error('Error clearing localStorage:', error);
        }
    }
};
const reportStorage = {
    /**
   * Get all reports
   * @returns {Array} Array of reports
   */ getAll () {
        return storage.get(STORAGE_KEYS.REPORTS, []);
    },
    /**
   * Get report by ID
   * @param {string} id - Report ID
   * @returns {Object|null} Report object or null
   */ getById (id) {
        const reports = this.getAll();
        return reports.find((report)=>report.id === id) || null;
    },
    /**
   * Save new report
   * @param {Object} reportData - Report data
   * @returns {Object} Saved report with generated ID
   */ save (reportData) {
        const reports = this.getAll();
        const newReport = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
            reference: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateReportReference"])(reportData.location.country, reportData.type),
            ...reportData,
            status: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REPORT_STATUS"].SUBMITTED.id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            timeline: [
                {
                    status: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REPORT_STATUS"].SUBMITTED.id,
                    timestamp: new Date().toISOString(),
                    message: 'Report submitted successfully'
                }
            ]
        };
        reports.push(newReport);
        storage.set(STORAGE_KEYS.REPORTS, reports);
        // Update statistics
        this.updateStatistics();
        return newReport;
    },
    /**
   * Update report
   * @param {string} id - Report ID
   * @param {Object} updates - Updates to apply
   * @returns {Object|null} Updated report or null
   */ update (id, updates) {
        const reports = this.getAll();
        const index = reports.findIndex((report)=>report.id === id);
        if (index === -1) return null;
        const updatedReport = {
            ...reports[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        // Add timeline entry if status changed
        if (updates.status && updates.status !== reports[index].status) {
            updatedReport.timeline = [
                ...reports[index].timeline,
                {
                    status: updates.status,
                    timestamp: new Date().toISOString(),
                    message: updates.statusMessage || `Status changed to ${updates.status}`
                }
            ];
        }
        reports[index] = updatedReport;
        storage.set(STORAGE_KEYS.REPORTS, reports);
        return updatedReport;
    },
    /**
   * Delete report
   * @param {string} id - Report ID
   * @returns {boolean} Success status
   */ delete (id) {
        const reports = this.getAll();
        const filteredReports = reports.filter((report)=>report.id !== id);
        if (filteredReports.length === reports.length) {
            return false; // Report not found
        }
        storage.set(STORAGE_KEYS.REPORTS, filteredReports);
        this.updateStatistics();
        return true;
    },
    /**
   * Get reports by filters
   * @param {Object} filters - Filter criteria
   * @returns {Array} Filtered reports
   */ getByFilters (filters = {}) {
        const reports = this.getAll();
        return reports.filter((report)=>{
            // Filter by status
            if (filters.status && report.status !== filters.status) {
                return false;
            }
            // Filter by type
            if (filters.type && report.type !== filters.type) {
                return false;
            }
            // Filter by country
            if (filters.country && report.location.country !== filters.country) {
                return false;
            }
            // Filter by date range
            if (filters.startDate) {
                const reportDate = new Date(report.createdAt);
                const startDate = new Date(filters.startDate);
                if (reportDate < startDate) return false;
            }
            if (filters.endDate) {
                const reportDate = new Date(report.createdAt);
                const endDate = new Date(filters.endDate);
                if (reportDate > endDate) return false;
            }
            return true;
        });
    },
    /**
   * Update statistics
   */ updateStatistics () {
        const reports = this.getAll();
        const stats = {
            total: reports.length,
            byStatus: {},
            byType: {},
            byCountry: {},
            lastUpdated: new Date().toISOString()
        };
        reports.forEach((report)=>{
            // Count by status
            stats.byStatus[report.status] = (stats.byStatus[report.status] || 0) + 1;
            // Count by type
            stats.byType[report.type] = (stats.byType[report.type] || 0) + 1;
            // Count by country
            stats.byCountry[report.location.country] = (stats.byCountry[report.location.country] || 0) + 1;
        });
        storage.set(STORAGE_KEYS.STATISTICS, stats);
    }
};
const userStorage = {
    /**
   * Get user data
   * @returns {Object} User data
   */ get () {
        return storage.get(STORAGE_KEYS.USER_DATA, {
            preferences: {
                language: 'en',
                notifications: true,
                anonymousReporting: false
            },
            profile: {
                name: '',
                email: '',
                phone: ''
            }
        });
    },
    /**
   * Save user data
   * @param {Object} userData - User data to save
   */ save (userData) {
        const currentData = this.get();
        const updatedData = {
            ...currentData,
            ...userData,
            lastUpdated: new Date().toISOString()
        };
        storage.set(STORAGE_KEYS.USER_DATA, updatedData);
    },
    /**
   * Update user preferences
   * @param {Object} preferences - Preferences to update
   */ updatePreferences (preferences) {
        const userData = this.get();
        userData.preferences = {
            ...userData.preferences,
            ...preferences
        };
        this.save(userData);
    },
    /**
   * Update user profile
   * @param {Object} profile - Profile data to update
   */ updateProfile (profile) {
        const userData = this.get();
        userData.profile = {
            ...userData.profile,
            ...profile
        };
        this.save(userData);
    }
};
const settingsStorage = {
    /**
   * Get settings
   * @returns {Object} Settings
   */ get () {
        return storage.get(STORAGE_KEYS.SETTINGS, {
            theme: 'dark',
            animations: true,
            autoLocation: true,
            notifications: true
        });
    },
    /**
   * Save settings
   * @param {Object} settings - Settings to save
   */ save (settings) {
        const currentSettings = this.get();
        const updatedSettings = {
            ...currentSettings,
            ...settings,
            lastUpdated: new Date().toISOString()
        };
        storage.set(STORAGE_KEYS.SETTINGS, updatedSettings);
    }
};
const statisticsStorage = {
    /**
   * Get statistics
   * @returns {Object} Statistics
   */ get () {
        return storage.get(STORAGE_KEYS.STATISTICS, {
            total: 0,
            byStatus: {},
            byType: {},
            byCountry: {},
            lastUpdated: new Date().toISOString()
        });
    },
    /**
   * Get community statistics (mock data for demo)
   * @returns {Object} Community statistics
   */ getCommunityStats () {
        const baseStats = this.get();
        // Add some mock community data for demo purposes
        return {
            ...baseStats,
            totalUsers: 12847,
            reportsThisMonth: 1234,
            resolvedThisMonth: 987,
            averageResponseTime: '2.3 hours',
            topReportTypes: [
                {
                    type: 'traffic',
                    count: 456,
                    percentage: 37
                },
                {
                    type: 'public',
                    count: 321,
                    percentage: 26
                },
                {
                    type: 'consumer',
                    count: 234,
                    percentage: 19
                },
                {
                    type: 'crime',
                    count: 123,
                    percentage: 10
                },
                {
                    type: 'other',
                    count: 100,
                    percentage: 8
                }
            ],
            recentActivity: [
                {
                    id: '1',
                    type: 'traffic',
                    location: 'Kuala Lumpur',
                    time: '2 minutes ago'
                },
                {
                    id: '2',
                    type: 'public',
                    location: 'Singapore',
                    time: '5 minutes ago'
                },
                {
                    id: '3',
                    type: 'consumer',
                    location: 'Penang',
                    time: '8 minutes ago'
                }
            ]
        };
    }
};
function initializeDemoData() {
    const reports = reportStorage.getAll();
    // Only initialize if no reports exist
    if (reports.length === 0) {
        const demoReports = [
            {
                type: 'traffic',
                title: 'Illegal Parking',
                description: 'Car parked in disabled parking space without permit',
                location: {
                    country: 'malaysia',
                    coordinates: {
                        lat: 3.1390,
                        lng: 101.6869
                    },
                    address: 'Kuala Lumpur, Malaysia'
                },
                evidence: [
                    {
                        type: 'image',
                        url: '/demo/parking-violation.jpg',
                        timestamp: new Date().toISOString()
                    }
                ],
                anonymous: false,
                reporter: {
                    name: 'Ahmad Rahman',
                    email: '<EMAIL>'
                }
            },
            {
                type: 'public',
                title: 'Noise Complaint',
                description: 'Loud music from construction site after 10 PM',
                location: {
                    country: 'singapore',
                    coordinates: {
                        lat: 1.3521,
                        lng: 103.8198
                    },
                    address: 'Central, Singapore'
                },
                evidence: [
                    {
                        type: 'audio',
                        url: '/demo/noise-complaint.mp3',
                        timestamp: new Date().toISOString()
                    }
                ],
                anonymous: true
            }
        ];
        demoReports.forEach((report)=>reportStorage.save(report));
    }
}
const __TURBOPACK__default__export__ = {
    storage,
    reportStorage,
    userStorage,
    settingsStorage,
    statisticsStorage,
    initializeDemoData
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$sections$2f$Hero$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/sections/Hero.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/storage.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function Home() {
    _s();
    // Initialize demo data on first load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Home.useEffect": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initializeDemoData"])();
        }
    }["Home.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
        className: "min-h-screen",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$sections$2f$Hero$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/page.js",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "h-screen bg-gray-900 flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-4xl font-bold text-white mb-4",
                            children: "More sections coming soon..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.js",
                            lineNumber: 20,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-400",
                            children: "Problem/Solution, Features, Testimonials, Pricing"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.js",
                            lineNumber: 21,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.js",
                    lineNumber: 19,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.js",
                lineNumber: 18,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.js",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
_s(Home, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_ee8cddab._.js.map