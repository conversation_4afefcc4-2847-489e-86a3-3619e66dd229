// ReportU Mock Data for Demo and Development

import { COUNTRIES, OFFENSE_TYPES, REPORT_STATUS } from './constants';

// Mock testimonials
export const mockTestimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Singapore Resident',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    text: 'ReportU made it so easy to report a traffic violation. The whole process took less than 2 minutes, and I got updates throughout!',
    location: 'Singapore',
    reportType: 'Traffic Violation'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Kuala Lumpur Resident',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    text: 'Finally, a platform that works across borders! Reported illegal dumping and the authorities responded within hours.',
    location: 'Malaysia',
    reportType: 'Public Disturbance'
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    role: 'Business Owner',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    text: 'The multi-language support is fantastic. I could report counterfeit goods in Tamil, and the process was seamless.',
    location: 'Singapore',
    reportType: 'Consumer Protection'
  },
  {
    id: 4,
    name: 'David Lim',
    role: 'Tourist',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4,
    text: 'As a tourist, I was unsure how to report an incident. ReportU guided me through everything step by step.',
    location: 'Malaysia',
    reportType: 'Crime Report'
  }
];

// Mock statistics for dashboard
export const mockStatistics = {
  totalReports: 15847,
  resolvedReports: 12456,
  activeUsers: 8923,
  averageResponseTime: '2.3 hours',
  monthlyGrowth: 23.5,
  
  reportsByType: [
    { type: 'traffic', count: 5847, percentage: 37, trend: '+12%' },
    { type: 'public', count: 4123, percentage: 26, trend: '+8%' },
    { type: 'consumer', count: 3012, percentage: 19, trend: '+15%' },
    { type: 'crime', count: 1865, percentage: 12, trend: '+5%' },
    { type: 'other', count: 1000, percentage: 6, trend: '+3%' }
  ],
  
  reportsByCountry: [
    { country: COUNTRIES.MALAYSIA, count: 9234, percentage: 58 },
    { country: COUNTRIES.SINGAPORE, count: 6613, percentage: 42 }
  ],
  
  reportsByStatus: [
    { status: REPORT_STATUS.RESOLVED.id, count: 12456, percentage: 78.6 },
    { status: REPORT_STATUS.PROCESSING.id, count: 2341, percentage: 14.8 },
    { status: REPORT_STATUS.INVESTIGATING.id, count: 789, percentage: 5.0 },
    { status: REPORT_STATUS.SUBMITTED.id, count: 261, percentage: 1.6 }
  ],
  
  recentActivity: [
    {
      id: 'act_1',
      type: 'traffic',
      title: 'Speeding Violation Reported',
      location: 'Orchard Road, Singapore',
      time: '2 minutes ago',
      status: REPORT_STATUS.SUBMITTED.id
    },
    {
      id: 'act_2',
      type: 'public',
      title: 'Noise Complaint Resolved',
      location: 'KLCC, Kuala Lumpur',
      time: '15 minutes ago',
      status: REPORT_STATUS.RESOLVED.id
    },
    {
      id: 'act_3',
      type: 'consumer',
      title: 'Counterfeit Goods Investigation',
      location: 'Bugis Street, Singapore',
      time: '1 hour ago',
      status: REPORT_STATUS.INVESTIGATING.id
    },
    {
      id: 'act_4',
      type: 'crime',
      title: 'Theft Report Processing',
      location: 'Petaling Jaya, Malaysia',
      time: '2 hours ago',
      status: REPORT_STATUS.PROCESSING.id
    }
  ]
};

// Mock feature highlights
export const mockFeatures = [
  {
    id: 'smart-routing',
    title: 'Smart Authority Routing',
    description: 'AI-powered system automatically routes your report to the correct authority based on offense type and location.',
    icon: '🎯',
    benefits: [
      'Automatic authority detection',
      'Cross-border functionality',
      'Real-time routing updates'
    ],
    demoUrl: '/demo/smart-routing'
  },
  {
    id: 'multimedia-evidence',
    title: 'Rich Evidence Support',
    description: 'Upload photos, videos, and audio recordings as evidence. Our system optimizes files for faster processing.',
    icon: '📸',
    benefits: [
      'Multiple file format support',
      'Automatic compression',
      'Secure cloud storage'
    ],
    demoUrl: '/demo/evidence-upload'
  },
  {
    id: 'real-time-tracking',
    title: 'Live Status Tracking',
    description: 'Track your report progress in real-time with detailed timeline and authority updates.',
    icon: '📊',
    benefits: [
      'Real-time status updates',
      'Detailed timeline view',
      'Push notifications'
    ],
    demoUrl: '/demo/tracking'
  },
  {
    id: 'multi-language',
    title: 'Multi-Language Support',
    description: 'Report in your preferred language with support for English, Malay, Mandarin, and Tamil.',
    icon: '🌐',
    benefits: [
      '4 language support',
      'Auto-translation',
      'Cultural localization'
    ],
    demoUrl: '/demo/languages'
  },
  {
    id: 'anonymous-reporting',
    title: 'Anonymous Reporting',
    description: 'Report sensitive issues anonymously while maintaining the ability to track progress.',
    icon: '🛡️',
    benefits: [
      'Complete anonymity',
      'Secure tracking codes',
      'Privacy protection'
    ],
    demoUrl: '/demo/anonymous'
  },
  {
    id: 'community-dashboard',
    title: 'Community Impact',
    description: 'See how your reports contribute to community safety with detailed analytics and insights.',
    icon: '🏘️',
    benefits: [
      'Community statistics',
      'Impact visualization',
      'Safety improvements'
    ],
    demoUrl: '/demo/community'
  }
];

// Mock pricing plans
export const mockPricingPlans = [
  {
    id: 'free',
    name: 'Community',
    price: 0,
    period: 'month',
    description: 'Perfect for individual citizens reporting occasional offenses',
    features: [
      'Up to 5 reports per month',
      'Basic status tracking',
      'Standard processing time',
      'Community dashboard access',
      'Email notifications'
    ],
    limitations: [
      'Limited to 5 reports monthly',
      'Standard support only',
      'Basic analytics'
    ],
    cta: 'Get Started Free',
    popular: false
  },
  {
    id: 'premium',
    name: 'Citizen Plus',
    price: 4.99,
    period: 'month',
    description: 'Enhanced features for active community members and frequent reporters',
    features: [
      'Unlimited reports',
      'Priority processing',
      'Advanced tracking & analytics',
      'Anonymous reporting',
      'SMS + Email notifications',
      'Premium support',
      'Export report history'
    ],
    limitations: [],
    cta: 'Start Premium Trial',
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Organization',
    price: 99,
    period: 'month',
    description: 'Comprehensive solution for businesses, NGOs, and government agencies',
    features: [
      'Bulk reporting capabilities',
      'Custom integrations',
      'Advanced analytics dashboard',
      'White-label options',
      'Dedicated account manager',
      'API access',
      'Custom workflows',
      'Priority support 24/7'
    ],
    limitations: [],
    cta: 'Contact Sales',
    popular: false
  }
];

// Mock FAQ data
export const mockFAQs = [
  {
    id: 1,
    question: 'How does cross-border reporting work?',
    answer: 'ReportU automatically detects your location and routes your report to the appropriate authority in Malaysia or Singapore. Our smart routing system ensures your report reaches the right department based on the offense type and jurisdiction.'
  },
  {
    id: 2,
    question: 'Is my personal information secure?',
    answer: 'Yes, we use end-to-end encryption and comply with GDPR and PDPA regulations. You can also choose to report anonymously while still tracking your report progress through a secure reference number.'
  },
  {
    id: 3,
    question: 'What types of evidence can I upload?',
    answer: 'You can upload photos (JPG, PNG, WebP), videos (MP4, MOV, WebM), and audio recordings (MP3, WAV, M4A). Files are automatically compressed and securely stored in the cloud.'
  },
  {
    id: 4,
    question: 'How long does it take for authorities to respond?',
    answer: 'Response times vary by authority and offense type, but our average response time is 2.3 hours. You\'ll receive real-time updates throughout the process via email and SMS notifications.'
  },
  {
    id: 5,
    question: 'Can I track multiple reports?',
    answer: 'Yes, your dashboard shows all your reports with real-time status updates. Premium users get advanced analytics and can export their report history.'
  },
  {
    id: 6,
    question: 'What languages are supported?',
    answer: 'ReportU supports English, Bahasa Malaysia, Mandarin Chinese, and Tamil. The interface and all communications are available in your preferred language.'
  }
];

// Mock demo scenarios for interactive demo
export const mockDemoScenarios = [
  {
    id: 'traffic-violation',
    title: 'Traffic Violation Report',
    description: 'Report a speeding vehicle with photo evidence',
    type: OFFENSE_TYPES.TRAFFIC.id,
    location: {
      country: COUNTRIES.SINGAPORE,
      coordinates: { lat: 1.3521, lng: 103.8198 },
      address: 'Orchard Road, Singapore'
    },
    evidence: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop',
        description: 'Speeding vehicle caught on camera'
      }
    ],
    expectedAuthority: 'LTA',
    estimatedTime: '2-4 hours'
  },
  {
    id: 'noise-complaint',
    title: 'Noise Complaint',
    description: 'Report excessive noise from construction site',
    type: OFFENSE_TYPES.PUBLIC.id,
    location: {
      country: COUNTRIES.MALAYSIA,
      coordinates: { lat: 3.1390, lng: 101.6869 },
      address: 'KLCC, Kuala Lumpur'
    },
    evidence: [
      {
        type: 'audio',
        url: '/demo/construction-noise.mp3',
        description: 'Recorded noise levels exceeding limits'
      }
    ],
    expectedAuthority: 'PDRM',
    estimatedTime: '1-2 hours'
  },
  {
    id: 'counterfeit-goods',
    title: 'Counterfeit Products',
    description: 'Report fake branded merchandise being sold',
    type: OFFENSE_TYPES.CONSUMER.id,
    location: {
      country: COUNTRIES.SINGAPORE,
      coordinates: { lat: 1.3006, lng: 103.8518 },
      address: 'Bugis Street, Singapore'
    },
    evidence: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop',
        description: 'Counterfeit products with poor quality'
      }
    ],
    expectedAuthority: 'CASE',
    estimatedTime: '4-6 hours'
  }
];

// Mock authority response templates
export const mockAuthorityResponses = [
  {
    authority: 'LTA',
    responses: [
      'Thank you for your report. We are reviewing the evidence and will take appropriate action.',
      'Investigation in progress. Traffic enforcement officers have been dispatched to the location.',
      'Report resolved. Summons has been issued to the vehicle owner. Thank you for helping keep our roads safe.'
    ]
  },
  {
    authority: 'JPJ',
    responses: [
      'Laporan anda telah diterima. Kami sedang menyiasat kes ini.',
      'Pegawai penguatkuasaan telah dihantar ke lokasi tersebut.',
      'Kes telah diselesaikan. Saman telah dikeluarkan. Terima kasih atas kerjasama anda.'
    ]
  },
  {
    authority: 'SPF',
    responses: [
      'Your report has been received and assigned to the appropriate department.',
      'Officers are investigating the matter. We may contact you for additional information.',
      'Case closed. Appropriate action has been taken. Thank you for your civic responsibility.'
    ]
  }
];

// Mock success stories
export const mockSuccessStories = [
  {
    id: 1,
    title: 'Traffic Safety Improved by 40%',
    description: 'Reports from Orchard Road led to increased enforcement, reducing accidents by 40% in 3 months.',
    location: 'Orchard Road, Singapore',
    impact: '40% reduction in accidents',
    reportsCount: 156,
    image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop'
  },
  {
    id: 2,
    title: 'Illegal Dumping Eliminated',
    description: 'Community reports helped authorities identify and stop illegal waste dumping in residential areas.',
    location: 'Petaling Jaya, Malaysia',
    impact: '100% elimination of illegal dumping',
    reportsCount: 89,
    image: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?w=400&h=300&fit=crop'
  },
  {
    id: 3,
    title: 'Counterfeit Market Shutdown',
    description: 'Consumer reports led to the shutdown of a major counterfeit goods operation.',
    location: 'Bugis Street, Singapore',
    impact: '$2M worth of fake goods seized',
    reportsCount: 234,
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop'
  }
];

export default {
  mockTestimonials,
  mockStatistics,
  mockFeatures,
  mockPricingPlans,
  mockFAQs,
  mockDemoScenarios,
  mockAuthorityResponses,
  mockSuccessStories
};
