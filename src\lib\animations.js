// ReportU Animation Utilities using GSAP

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Animation configurations
export const ANIMATION_CONFIG = {
  duration: {
    fast: 0.2,
    normal: 0.3,
    slow: 0.5,
    extraSlow: 1.0
  },
  easing: {
    power1: 'power1.out',
    power2: 'power2.out',
    power3: 'power3.out',
    back: 'back.out(1.7)',
    elastic: 'elastic.out(1, 0.3)',
    bounce: 'bounce.out'
  }
};

/**
 * Initialize GSAP defaults
 */
export function initializeGSAP() {
  if (typeof window === 'undefined') return;
  
  // Set GSAP defaults
  gsap.defaults({
    duration: ANIMATION_CONFIG.duration.normal,
    ease: ANIMATION_CONFIG.easing.power2
  });
  
  // Refresh ScrollTrigger on route changes
  ScrollTrigger.refresh();
}

/**
 * Hero section animations
 */
export const heroAnimations = {
  /**
   * Animate hero title entrance
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  titleEntrance(selector, options = {}) {
    const tl = gsap.timeline();
    
    tl.from(selector, {
      y: 100,
      opacity: 0,
      duration: 1,
      ease: ANIMATION_CONFIG.easing.power3,
      ...options
    });
    
    return tl;
  },

  /**
   * Animate hero subtitle entrance
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  subtitleEntrance(selector, options = {}) {
    const tl = gsap.timeline();
    
    tl.from(selector, {
      y: 50,
      opacity: 0,
      duration: 0.8,
      ease: ANIMATION_CONFIG.easing.power2,
      delay: 0.3,
      ...options
    });
    
    return tl;
  },

  /**
   * Animate CTA button entrance
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  ctaEntrance(selector, options = {}) {
    const tl = gsap.timeline();
    
    tl.from(selector, {
      scale: 0,
      opacity: 0,
      duration: 0.6,
      ease: ANIMATION_CONFIG.easing.back,
      delay: 0.6,
      ...options
    });
    
    return tl;
  },

  /**
   * Complete hero section entrance animation
   * @param {Object} selectors - Object containing selectors for different elements
   */
  completeEntrance(selectors) {
    const tl = gsap.timeline();
    
    tl.add(this.titleEntrance(selectors.title))
      .add(this.subtitleEntrance(selectors.subtitle), '-=0.5')
      .add(this.ctaEntrance(selectors.cta), '-=0.3');
    
    return tl;
  }
};

/**
 * Scroll-triggered animations
 */
export const scrollAnimations = {
  /**
   * Fade in from bottom on scroll
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  fadeInUp(selector, options = {}) {
    gsap.from(selector, {
      y: 100,
      opacity: 0,
      duration: 0.8,
      ease: ANIMATION_CONFIG.easing.power2,
      scrollTrigger: {
        trigger: selector,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
        ...options.scrollTrigger
      },
      ...options
    });
  },

  /**
   * Stagger animation for multiple elements
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  staggerFadeIn(selector, options = {}) {
    gsap.from(selector, {
      y: 100,
      opacity: 0,
      duration: 0.8,
      ease: ANIMATION_CONFIG.easing.power2,
      stagger: 0.2,
      scrollTrigger: {
        trigger: selector,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
        ...options.scrollTrigger
      },
      ...options
    });
  },

  /**
   * Scale in animation
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  scaleIn(selector, options = {}) {
    gsap.from(selector, {
      scale: 0,
      opacity: 0,
      duration: 0.6,
      ease: ANIMATION_CONFIG.easing.back,
      scrollTrigger: {
        trigger: selector,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
        ...options.scrollTrigger
      },
      ...options
    });
  },

  /**
   * Slide in from left
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  slideInLeft(selector, options = {}) {
    gsap.from(selector, {
      x: -100,
      opacity: 0,
      duration: 0.8,
      ease: ANIMATION_CONFIG.easing.power2,
      scrollTrigger: {
        trigger: selector,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
        ...options.scrollTrigger
      },
      ...options
    });
  },

  /**
   * Slide in from right
   * @param {string} selector - Element selector
   * @param {Object} options - Animation options
   */
  slideInRight(selector, options = {}) {
    gsap.from(selector, {
      x: 100,
      opacity: 0,
      duration: 0.8,
      ease: ANIMATION_CONFIG.easing.power2,
      scrollTrigger: {
        trigger: selector,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
        ...options.scrollTrigger
      },
      ...options
    });
  }
};

/**
 * Hover animations
 */
export const hoverAnimations = {
  /**
   * Button hover effect
   * @param {string} selector - Element selector
   */
  buttonHover(selector) {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(element => {
      const hoverTl = gsap.timeline({ paused: true });
      
      hoverTl.to(element, {
        scale: 1.05,
        y: -2,
        boxShadow: '0 20px 40px rgba(59, 130, 246, 0.3)',
        duration: ANIMATION_CONFIG.duration.fast,
        ease: ANIMATION_CONFIG.easing.power2
      });
      
      element.addEventListener('mouseenter', () => hoverTl.play());
      element.addEventListener('mouseleave', () => hoverTl.reverse());
    });
  },

  /**
   * Card hover effect
   * @param {string} selector - Element selector
   */
  cardHover(selector) {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(element => {
      const hoverTl = gsap.timeline({ paused: true });
      
      hoverTl.to(element, {
        y: -10,
        scale: 1.02,
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.2)',
        duration: ANIMATION_CONFIG.duration.normal,
        ease: ANIMATION_CONFIG.easing.power2
      });
      
      element.addEventListener('mouseenter', () => hoverTl.play());
      element.addEventListener('mouseleave', () => hoverTl.reverse());
    });
  },

  /**
   * Icon hover effect
   * @param {string} selector - Element selector
   */
  iconHover(selector) {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(element => {
      const hoverTl = gsap.timeline({ paused: true });
      
      hoverTl.to(element, {
        rotation: 360,
        scale: 1.2,
        duration: ANIMATION_CONFIG.duration.slow,
        ease: ANIMATION_CONFIG.easing.elastic
      });
      
      element.addEventListener('mouseenter', () => hoverTl.play());
      element.addEventListener('mouseleave', () => hoverTl.reverse());
    });
  }
};

/**
 * Loading animations
 */
export const loadingAnimations = {
  /**
   * Pulse animation
   * @param {string} selector - Element selector
   */
  pulse(selector) {
    gsap.to(selector, {
      scale: 1.1,
      opacity: 0.7,
      duration: 1,
      ease: 'power2.inOut',
      repeat: -1,
      yoyo: true
    });
  },

  /**
   * Spinner animation
   * @param {string} selector - Element selector
   */
  spinner(selector) {
    gsap.to(selector, {
      rotation: 360,
      duration: 1,
      ease: 'none',
      repeat: -1
    });
  },

  /**
   * Dots loading animation
   * @param {string} selector - Element selector for dots container
   */
  dots(selector) {
    const dots = document.querySelectorAll(`${selector} .dot`);
    
    gsap.to(dots, {
      y: -10,
      duration: 0.5,
      ease: 'power2.inOut',
      stagger: 0.1,
      repeat: -1,
      yoyo: true
    });
  }
};

/**
 * Page transition animations
 */
export const pageTransitions = {
  /**
   * Fade in page
   * @param {string} selector - Element selector
   */
  fadeIn(selector) {
    gsap.from(selector, {
      opacity: 0,
      duration: 0.5,
      ease: ANIMATION_CONFIG.easing.power2
    });
  },

  /**
   * Slide in page from right
   * @param {string} selector - Element selector
   */
  slideInFromRight(selector) {
    gsap.from(selector, {
      x: '100%',
      duration: 0.5,
      ease: ANIMATION_CONFIG.easing.power2
    });
  },

  /**
   * Scale in page
   * @param {string} selector - Element selector
   */
  scaleIn(selector) {
    gsap.from(selector, {
      scale: 0.8,
      opacity: 0,
      duration: 0.5,
      ease: ANIMATION_CONFIG.easing.back
    });
  }
};

/**
 * Form animations
 */
export const formAnimations = {
  /**
   * Input focus animation
   * @param {string} selector - Element selector
   */
  inputFocus(selector) {
    const inputs = document.querySelectorAll(selector);
    
    inputs.forEach(input => {
      const focusTl = gsap.timeline({ paused: true });
      
      focusTl.to(input, {
        scale: 1.02,
        boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',
        duration: ANIMATION_CONFIG.duration.fast,
        ease: ANIMATION_CONFIG.easing.power2
      });
      
      input.addEventListener('focus', () => focusTl.play());
      input.addEventListener('blur', () => focusTl.reverse());
    });
  },

  /**
   * Form submission success animation
   * @param {string} selector - Element selector
   */
  submitSuccess(selector) {
    const tl = gsap.timeline();
    
    tl.to(selector, {
      scale: 1.1,
      duration: 0.2,
      ease: ANIMATION_CONFIG.easing.power2
    })
    .to(selector, {
      scale: 1,
      duration: 0.3,
      ease: ANIMATION_CONFIG.easing.back
    });
    
    return tl;
  },

  /**
   * Error shake animation
   * @param {string} selector - Element selector
   */
  errorShake(selector) {
    gsap.to(selector, {
      x: [-10, 10, -10, 10, 0],
      duration: 0.5,
      ease: ANIMATION_CONFIG.easing.power2
    });
  }
};

/**
 * Utility functions
 */
export const animationUtils = {
  /**
   * Kill all animations for an element
   * @param {string} selector - Element selector
   */
  killAll(selector) {
    gsap.killTweensOf(selector);
  },

  /**
   * Set initial state for animations
   * @param {string} selector - Element selector
   * @param {Object} properties - CSS properties to set
   */
  setInitialState(selector, properties) {
    gsap.set(selector, properties);
  },

  /**
   * Refresh ScrollTrigger
   */
  refreshScrollTrigger() {
    if (typeof window !== 'undefined') {
      ScrollTrigger.refresh();
    }
  },

  /**
   * Create a master timeline
   * @returns {gsap.timeline} GSAP timeline
   */
  createTimeline() {
    return gsap.timeline();
  }
};

export default {
  ANIMATION_CONFIG,
  initializeGSAP,
  heroAnimations,
  scrollAnimations,
  hoverAnimations,
  loadingAnimations,
  pageTransitions,
  formAnimations,
  animationUtils
};
