{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/lib/animations.js"], "sourcesContent": ["// ReportU Animation Utilities using GSAP\n\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\n\n// Register GSAP plugins\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\n// Animation configurations\nexport const ANIMATION_CONFIG = {\n  duration: {\n    fast: 0.2,\n    normal: 0.3,\n    slow: 0.5,\n    extraSlow: 1.0\n  },\n  easing: {\n    power1: 'power1.out',\n    power2: 'power2.out',\n    power3: 'power3.out',\n    back: 'back.out(1.7)',\n    elastic: 'elastic.out(1, 0.3)',\n    bounce: 'bounce.out'\n  }\n};\n\n/**\n * Initialize GSAP defaults\n */\nexport function initializeGSAP() {\n  if (typeof window === 'undefined') return;\n  \n  // Set GSAP defaults\n  gsap.defaults({\n    duration: ANIMATION_CONFIG.duration.normal,\n    ease: ANIMATION_CONFIG.easing.power2\n  });\n  \n  // Refresh ScrollTrigger on route changes\n  ScrollTrigger.refresh();\n}\n\n/**\n * Hero section animations\n */\nexport const heroAnimations = {\n  /**\n   * Animate hero title entrance\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  titleEntrance(selector, options = {}) {\n    const tl = gsap.timeline();\n    \n    tl.from(selector, {\n      y: 100,\n      opacity: 0,\n      duration: 1,\n      ease: ANIMATION_CONFIG.easing.power3,\n      ...options\n    });\n    \n    return tl;\n  },\n\n  /**\n   * Animate hero subtitle entrance\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  subtitleEntrance(selector, options = {}) {\n    const tl = gsap.timeline();\n    \n    tl.from(selector, {\n      y: 50,\n      opacity: 0,\n      duration: 0.8,\n      ease: ANIMATION_CONFIG.easing.power2,\n      delay: 0.3,\n      ...options\n    });\n    \n    return tl;\n  },\n\n  /**\n   * Animate CTA button entrance\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  ctaEntrance(selector, options = {}) {\n    const tl = gsap.timeline();\n    \n    tl.from(selector, {\n      scale: 0,\n      opacity: 0,\n      duration: 0.6,\n      ease: ANIMATION_CONFIG.easing.back,\n      delay: 0.6,\n      ...options\n    });\n    \n    return tl;\n  },\n\n  /**\n   * Complete hero section entrance animation\n   * @param {Object} selectors - Object containing selectors for different elements\n   */\n  completeEntrance(selectors) {\n    const tl = gsap.timeline();\n    \n    tl.add(this.titleEntrance(selectors.title))\n      .add(this.subtitleEntrance(selectors.subtitle), '-=0.5')\n      .add(this.ctaEntrance(selectors.cta), '-=0.3');\n    \n    return tl;\n  }\n};\n\n/**\n * Scroll-triggered animations\n */\nexport const scrollAnimations = {\n  /**\n   * Fade in from bottom on scroll\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  fadeInUp(selector, options = {}) {\n    gsap.from(selector, {\n      y: 100,\n      opacity: 0,\n      duration: 0.8,\n      ease: ANIMATION_CONFIG.easing.power2,\n      scrollTrigger: {\n        trigger: selector,\n        start: 'top 80%',\n        end: 'bottom 20%',\n        toggleActions: 'play none none reverse',\n        ...options.scrollTrigger\n      },\n      ...options\n    });\n  },\n\n  /**\n   * Stagger animation for multiple elements\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  staggerFadeIn(selector, options = {}) {\n    gsap.from(selector, {\n      y: 100,\n      opacity: 0,\n      duration: 0.8,\n      ease: ANIMATION_CONFIG.easing.power2,\n      stagger: 0.2,\n      scrollTrigger: {\n        trigger: selector,\n        start: 'top 80%',\n        end: 'bottom 20%',\n        toggleActions: 'play none none reverse',\n        ...options.scrollTrigger\n      },\n      ...options\n    });\n  },\n\n  /**\n   * Scale in animation\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  scaleIn(selector, options = {}) {\n    gsap.from(selector, {\n      scale: 0,\n      opacity: 0,\n      duration: 0.6,\n      ease: ANIMATION_CONFIG.easing.back,\n      scrollTrigger: {\n        trigger: selector,\n        start: 'top 80%',\n        end: 'bottom 20%',\n        toggleActions: 'play none none reverse',\n        ...options.scrollTrigger\n      },\n      ...options\n    });\n  },\n\n  /**\n   * Slide in from left\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  slideInLeft(selector, options = {}) {\n    gsap.from(selector, {\n      x: -100,\n      opacity: 0,\n      duration: 0.8,\n      ease: ANIMATION_CONFIG.easing.power2,\n      scrollTrigger: {\n        trigger: selector,\n        start: 'top 80%',\n        end: 'bottom 20%',\n        toggleActions: 'play none none reverse',\n        ...options.scrollTrigger\n      },\n      ...options\n    });\n  },\n\n  /**\n   * Slide in from right\n   * @param {string} selector - Element selector\n   * @param {Object} options - Animation options\n   */\n  slideInRight(selector, options = {}) {\n    gsap.from(selector, {\n      x: 100,\n      opacity: 0,\n      duration: 0.8,\n      ease: ANIMATION_CONFIG.easing.power2,\n      scrollTrigger: {\n        trigger: selector,\n        start: 'top 80%',\n        end: 'bottom 20%',\n        toggleActions: 'play none none reverse',\n        ...options.scrollTrigger\n      },\n      ...options\n    });\n  }\n};\n\n/**\n * Hover animations\n */\nexport const hoverAnimations = {\n  /**\n   * Button hover effect\n   * @param {string} selector - Element selector\n   */\n  buttonHover(selector) {\n    const elements = document.querySelectorAll(selector);\n    \n    elements.forEach(element => {\n      const hoverTl = gsap.timeline({ paused: true });\n      \n      hoverTl.to(element, {\n        scale: 1.05,\n        y: -2,\n        boxShadow: '0 20px 40px rgba(59, 130, 246, 0.3)',\n        duration: ANIMATION_CONFIG.duration.fast,\n        ease: ANIMATION_CONFIG.easing.power2\n      });\n      \n      element.addEventListener('mouseenter', () => hoverTl.play());\n      element.addEventListener('mouseleave', () => hoverTl.reverse());\n    });\n  },\n\n  /**\n   * Card hover effect\n   * @param {string} selector - Element selector\n   */\n  cardHover(selector) {\n    const elements = document.querySelectorAll(selector);\n    \n    elements.forEach(element => {\n      const hoverTl = gsap.timeline({ paused: true });\n      \n      hoverTl.to(element, {\n        y: -10,\n        scale: 1.02,\n        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.2)',\n        duration: ANIMATION_CONFIG.duration.normal,\n        ease: ANIMATION_CONFIG.easing.power2\n      });\n      \n      element.addEventListener('mouseenter', () => hoverTl.play());\n      element.addEventListener('mouseleave', () => hoverTl.reverse());\n    });\n  },\n\n  /**\n   * Icon hover effect\n   * @param {string} selector - Element selector\n   */\n  iconHover(selector) {\n    const elements = document.querySelectorAll(selector);\n    \n    elements.forEach(element => {\n      const hoverTl = gsap.timeline({ paused: true });\n      \n      hoverTl.to(element, {\n        rotation: 360,\n        scale: 1.2,\n        duration: ANIMATION_CONFIG.duration.slow,\n        ease: ANIMATION_CONFIG.easing.elastic\n      });\n      \n      element.addEventListener('mouseenter', () => hoverTl.play());\n      element.addEventListener('mouseleave', () => hoverTl.reverse());\n    });\n  }\n};\n\n/**\n * Loading animations\n */\nexport const loadingAnimations = {\n  /**\n   * Pulse animation\n   * @param {string} selector - Element selector\n   */\n  pulse(selector) {\n    gsap.to(selector, {\n      scale: 1.1,\n      opacity: 0.7,\n      duration: 1,\n      ease: 'power2.inOut',\n      repeat: -1,\n      yoyo: true\n    });\n  },\n\n  /**\n   * Spinner animation\n   * @param {string} selector - Element selector\n   */\n  spinner(selector) {\n    gsap.to(selector, {\n      rotation: 360,\n      duration: 1,\n      ease: 'none',\n      repeat: -1\n    });\n  },\n\n  /**\n   * Dots loading animation\n   * @param {string} selector - Element selector for dots container\n   */\n  dots(selector) {\n    const dots = document.querySelectorAll(`${selector} .dot`);\n    \n    gsap.to(dots, {\n      y: -10,\n      duration: 0.5,\n      ease: 'power2.inOut',\n      stagger: 0.1,\n      repeat: -1,\n      yoyo: true\n    });\n  }\n};\n\n/**\n * Page transition animations\n */\nexport const pageTransitions = {\n  /**\n   * Fade in page\n   * @param {string} selector - Element selector\n   */\n  fadeIn(selector) {\n    gsap.from(selector, {\n      opacity: 0,\n      duration: 0.5,\n      ease: ANIMATION_CONFIG.easing.power2\n    });\n  },\n\n  /**\n   * Slide in page from right\n   * @param {string} selector - Element selector\n   */\n  slideInFromRight(selector) {\n    gsap.from(selector, {\n      x: '100%',\n      duration: 0.5,\n      ease: ANIMATION_CONFIG.easing.power2\n    });\n  },\n\n  /**\n   * Scale in page\n   * @param {string} selector - Element selector\n   */\n  scaleIn(selector) {\n    gsap.from(selector, {\n      scale: 0.8,\n      opacity: 0,\n      duration: 0.5,\n      ease: ANIMATION_CONFIG.easing.back\n    });\n  }\n};\n\n/**\n * Form animations\n */\nexport const formAnimations = {\n  /**\n   * Input focus animation\n   * @param {string} selector - Element selector\n   */\n  inputFocus(selector) {\n    const inputs = document.querySelectorAll(selector);\n    \n    inputs.forEach(input => {\n      const focusTl = gsap.timeline({ paused: true });\n      \n      focusTl.to(input, {\n        scale: 1.02,\n        boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',\n        duration: ANIMATION_CONFIG.duration.fast,\n        ease: ANIMATION_CONFIG.easing.power2\n      });\n      \n      input.addEventListener('focus', () => focusTl.play());\n      input.addEventListener('blur', () => focusTl.reverse());\n    });\n  },\n\n  /**\n   * Form submission success animation\n   * @param {string} selector - Element selector\n   */\n  submitSuccess(selector) {\n    const tl = gsap.timeline();\n    \n    tl.to(selector, {\n      scale: 1.1,\n      duration: 0.2,\n      ease: ANIMATION_CONFIG.easing.power2\n    })\n    .to(selector, {\n      scale: 1,\n      duration: 0.3,\n      ease: ANIMATION_CONFIG.easing.back\n    });\n    \n    return tl;\n  },\n\n  /**\n   * Error shake animation\n   * @param {string} selector - Element selector\n   */\n  errorShake(selector) {\n    gsap.to(selector, {\n      x: [-10, 10, -10, 10, 0],\n      duration: 0.5,\n      ease: ANIMATION_CONFIG.easing.power2\n    });\n  }\n};\n\n/**\n * Utility functions\n */\nexport const animationUtils = {\n  /**\n   * Kill all animations for an element\n   * @param {string} selector - Element selector\n   */\n  killAll(selector) {\n    gsap.killTweensOf(selector);\n  },\n\n  /**\n   * Set initial state for animations\n   * @param {string} selector - Element selector\n   * @param {Object} properties - CSS properties to set\n   */\n  setInitialState(selector, properties) {\n    gsap.set(selector, properties);\n  },\n\n  /**\n   * Refresh ScrollTrigger\n   */\n  refreshScrollTrigger() {\n    if (typeof window !== 'undefined') {\n      ScrollTrigger.refresh();\n    }\n  },\n\n  /**\n   * Create a master timeline\n   * @returns {gsap.timeline} GSAP timeline\n   */\n  createTimeline() {\n    return gsap.timeline();\n  }\n};\n\nexport default {\n  ANIMATION_CONFIG,\n  initializeGSAP,\n  heroAnimations,\n  scrollAnimations,\n  hoverAnimations,\n  loadingAnimations,\n  pageTransitions,\n  formAnimations,\n  animationUtils\n};\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;;;;;AAEzC;AACA;;;AAEA,wBAAwB;AACxB,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,WAAW;IACb;IACA,QAAQ;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;IACV;AACF;AAKO,SAAS;IACd,uCAAmC;;IAAM;IAEzC,oBAAoB;IACpB,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;QACZ,UAAU,iBAAiB,QAAQ,CAAC,MAAM;QAC1C,MAAM,iBAAiB,MAAM,CAAC,MAAM;IACtC;IAEA,yCAAyC;IACzC,wIAAA,CAAA,gBAAa,CAAC,OAAO;AACvB;AAKO,MAAM,iBAAiB;IAC5B;;;;GAIC,GACD,eAAc,QAAQ,EAAE,UAAU,CAAC,CAAC;QAClC,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,GAAG,IAAI,CAAC,UAAU;YAChB,GAAG;YACH,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACpC,GAAG,OAAO;QACZ;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,kBAAiB,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrC,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,GAAG,IAAI,CAAC,UAAU;YAChB,GAAG;YACH,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACpC,OAAO;YACP,GAAG,OAAO;QACZ;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,aAAY,QAAQ,EAAE,UAAU,CAAC,CAAC;QAChC,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,GAAG,IAAI,CAAC,UAAU;YAChB,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,IAAI;YAClC,OAAO;YACP,GAAG,OAAO;QACZ;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,kBAAiB,SAAS;QACxB,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,GAAG,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,GACtC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,QAAQ,GAAG,SAC/C,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,GAAG;QAExC,OAAO;IACT;AACF;AAKO,MAAM,mBAAmB;IAC9B;;;;GAIC,GACD,UAAS,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC7B,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,GAAG;YACH,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACpC,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;gBACf,GAAG,QAAQ,aAAa;YAC1B;YACA,GAAG,OAAO;QACZ;IACF;IAEA;;;;GAIC,GACD,eAAc,QAAQ,EAAE,UAAU,CAAC,CAAC;QAClC,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,GAAG;YACH,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACpC,SAAS;YACT,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;gBACf,GAAG,QAAQ,aAAa;YAC1B;YACA,GAAG,OAAO;QACZ;IACF;IAEA;;;;GAIC,GACD,SAAQ,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC5B,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,IAAI;YAClC,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;gBACf,GAAG,QAAQ,aAAa;YAC1B;YACA,GAAG,OAAO;QACZ;IACF;IAEA;;;;GAIC,GACD,aAAY,QAAQ,EAAE,UAAU,CAAC,CAAC;QAChC,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,GAAG,CAAC;YACJ,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACpC,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;gBACf,GAAG,QAAQ,aAAa;YAC1B;YACA,GAAG,OAAO;QACZ;IACF;IAEA;;;;GAIC,GACD,cAAa,QAAQ,EAAE,UAAU,CAAC,CAAC;QACjC,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,GAAG;YACH,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACpC,eAAe;gBACb,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,eAAe;gBACf,GAAG,QAAQ,aAAa;YAC1B;YACA,GAAG,OAAO;QACZ;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;;GAGC,GACD,aAAY,QAAQ;QAClB,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAE3C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,UAAU,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBAAE,QAAQ;YAAK;YAE7C,QAAQ,EAAE,CAAC,SAAS;gBAClB,OAAO;gBACP,GAAG,CAAC;gBACJ,WAAW;gBACX,UAAU,iBAAiB,QAAQ,CAAC,IAAI;gBACxC,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACtC;YAEA,QAAQ,gBAAgB,CAAC,cAAc,IAAM,QAAQ,IAAI;YACzD,QAAQ,gBAAgB,CAAC,cAAc,IAAM,QAAQ,OAAO;QAC9D;IACF;IAEA;;;GAGC,GACD,WAAU,QAAQ;QAChB,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAE3C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,UAAU,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBAAE,QAAQ;YAAK;YAE7C,QAAQ,EAAE,CAAC,SAAS;gBAClB,GAAG,CAAC;gBACJ,OAAO;gBACP,WAAW;gBACX,UAAU,iBAAiB,QAAQ,CAAC,MAAM;gBAC1C,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACtC;YAEA,QAAQ,gBAAgB,CAAC,cAAc,IAAM,QAAQ,IAAI;YACzD,QAAQ,gBAAgB,CAAC,cAAc,IAAM,QAAQ,OAAO;QAC9D;IACF;IAEA;;;GAGC,GACD,WAAU,QAAQ;QAChB,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAE3C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,UAAU,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBAAE,QAAQ;YAAK;YAE7C,QAAQ,EAAE,CAAC,SAAS;gBAClB,UAAU;gBACV,OAAO;gBACP,UAAU,iBAAiB,QAAQ,CAAC,IAAI;gBACxC,MAAM,iBAAiB,MAAM,CAAC,OAAO;YACvC;YAEA,QAAQ,gBAAgB,CAAC,cAAc,IAAM,QAAQ,IAAI;YACzD,QAAQ,gBAAgB,CAAC,cAAc,IAAM,QAAQ,OAAO;QAC9D;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B;;;GAGC,GACD,OAAM,QAAQ;QACZ,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,UAAU;YAChB,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;YACN,QAAQ,CAAC;YACT,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,SAAQ,QAAQ;QACd,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,UAAU;YAChB,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ,CAAC;QACX;IACF;IAEA;;;GAGC,GACD,MAAK,QAAQ;QACX,MAAM,OAAO,SAAS,gBAAgB,CAAC,GAAG,SAAS,KAAK,CAAC;QAEzD,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;YACZ,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,SAAS;YACT,QAAQ,CAAC;YACT,MAAM;QACR;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;;GAGC,GACD,QAAO,QAAQ;QACb,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;QACtC;IACF;IAEA;;;GAGC,GACD,kBAAiB,QAAQ;QACvB,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,GAAG;YACH,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;QACtC;IACF;IAEA;;;GAGC,GACD,SAAQ,QAAQ;QACd,gJAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU;YAClB,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,IAAI;QACpC;IACF;AACF;AAKO,MAAM,iBAAiB;IAC5B;;;GAGC,GACD,YAAW,QAAQ;QACjB,MAAM,SAAS,SAAS,gBAAgB,CAAC;QAEzC,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,UAAU,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBAAE,QAAQ;YAAK;YAE7C,QAAQ,EAAE,CAAC,OAAO;gBAChB,OAAO;gBACP,WAAW;gBACX,UAAU,iBAAiB,QAAQ,CAAC,IAAI;gBACxC,MAAM,iBAAiB,MAAM,CAAC,MAAM;YACtC;YAEA,MAAM,gBAAgB,CAAC,SAAS,IAAM,QAAQ,IAAI;YAClD,MAAM,gBAAgB,CAAC,QAAQ,IAAM,QAAQ,OAAO;QACtD;IACF;IAEA;;;GAGC,GACD,eAAc,QAAQ;QACpB,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;QAExB,GAAG,EAAE,CAAC,UAAU;YACd,OAAO;YACP,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;QACtC,GACC,EAAE,CAAC,UAAU;YACZ,OAAO;YACP,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,IAAI;QACpC;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,YAAW,QAAQ;QACjB,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,UAAU;YAChB,GAAG;gBAAC,CAAC;gBAAI;gBAAI,CAAC;gBAAI;gBAAI;aAAE;YACxB,UAAU;YACV,MAAM,iBAAiB,MAAM,CAAC,MAAM;QACtC;IACF;AACF;AAKO,MAAM,iBAAiB;IAC5B;;;GAGC,GACD,SAAQ,QAAQ;QACd,gJAAA,CAAA,OAAI,CAAC,YAAY,CAAC;IACpB;IAEA;;;;GAIC,GACD,iBAAgB,QAAQ,EAAE,UAAU;QAClC,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU;IACrB;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,wIAAA,CAAA,gBAAa,CAAC,OAAO;QACvB;IACF;IAEA;;;GAGC,GACD;QACE,OAAO,gJAAA,CAAA,OAAI,CAAC,QAAQ;IACtB;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/components/sections/Hero.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport Link from 'next/link';\nimport { ChevronRightIcon, PlayIcon } from '@heroicons/react/24/outline';\nimport { gsap } from 'gsap';\nimport { heroAnimations } from '@/lib/animations';\n\nexport default function Hero() {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const ctaRef = useRef(null);\n  const statsRef = useRef(null);\n  const [vantaEffect, setVantaEffect] = useState(null);\n  const vantaRef = useRef(null);\n\n  // Initialize Vanta.js background effect\n  useEffect(() => {\n    let vanta = null;\n    \n    const initVanta = async () => {\n      if (typeof window !== 'undefined' && vantaRef.current) {\n        try {\n          const VANTA = (await import('vanta')).default;\n          const THREE = (await import('three')).default;\n          \n          vanta = VANTA.BIRDS({\n            el: vantaRef.current,\n            THREE: THREE,\n            mouseControls: true,\n            touchControls: true,\n            gyroControls: false,\n            minHeight: 200.00,\n            minWidth: 200.00,\n            scale: 1.00,\n            scaleMobile: 1.00,\n            backgroundColor: 0x0a0a0a,\n            color1: 0x3b82f6,\n            color2: 0x8b5cf6,\n            colorMode: 'variance',\n            birdSize: 1.20,\n            wingSpan: 25.00,\n            speedLimit: 4.00,\n            separation: 20.00,\n            alignment: 20.00,\n            cohesion: 20.00,\n            quantity: 3.00\n          });\n          \n          setVantaEffect(vanta);\n        } catch (error) {\n          console.error('Error loading Vanta.js:', error);\n        }\n      }\n    };\n\n    initVanta();\n\n    return () => {\n      if (vanta) vanta.destroy();\n    };\n  }, []);\n\n  // Initialize GSAP animations\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const tl = gsap.timeline({ delay: 0.5 });\n      \n      // Set initial states\n      gsap.set([titleRef.current, subtitleRef.current, ctaRef.current, statsRef.current], {\n        opacity: 0,\n        y: 50\n      });\n\n      // Animate elements in sequence\n      tl.to(titleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: 'power3.out'\n      })\n      .to(subtitleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 0.8,\n        ease: 'power2.out'\n      }, '-=0.5')\n      .to(ctaRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 0.6,\n        ease: 'back.out(1.7)'\n      }, '-=0.3')\n      .to(statsRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 0.8,\n        ease: 'power2.out'\n      }, '-=0.4');\n\n      // Add floating animation to the hero section\n      gsap.to(heroRef.current, {\n        y: -10,\n        duration: 3,\n        ease: 'power1.inOut',\n        repeat: -1,\n        yoyo: true\n      });\n    }\n  }, []);\n\n  // Handle CTA button hover\n  const handleCtaHover = (isHover) => {\n    if (typeof window !== 'undefined') {\n      gsap.to(ctaRef.current, {\n        scale: isHover ? 1.05 : 1,\n        y: isHover ? -2 : 0,\n        duration: 0.3,\n        ease: 'power2.out'\n      });\n    }\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Vanta.js Background */}\n      <div \n        ref={vantaRef}\n        className=\"absolute inset-0 z-0\"\n        style={{ width: '100%', height: '100%' }}\n      />\n      \n      {/* Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-gray-950/50 to-gray-950 z-10\" />\n      \n      {/* Content */}\n      <div ref={heroRef} className=\"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        {/* Main Title */}\n        <h1 \n          ref={titleRef}\n          className=\"text-responsive-xl font-bold mb-6 gradient-text\"\n        >\n          Report Offenses Across\n          <br />\n          <span className=\"text-white\">Malaysia & Singapore</span>\n        </h1>\n        \n        {/* Subtitle */}\n        <p \n          ref={subtitleRef}\n          className=\"text-responsive-md text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed\"\n        >\n          Submit reports with multimedia evidence that are automatically routed to the appropriate authorities. \n          Track progress in real-time and contribute to safer communities.\n        </p>\n        \n        {/* CTA Buttons */}\n        <div ref={ctaRef} className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n          <Link \n            href=\"/report\"\n            className=\"btn-primary text-lg px-8 py-4 group\"\n            onMouseEnter={() => handleCtaHover(true)}\n            onMouseLeave={() => handleCtaHover(false)}\n          >\n            Report Now\n            <ChevronRightIcon className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n          </Link>\n          \n          <Link \n            href=\"/demo\"\n            className=\"btn-secondary text-lg px-8 py-4 group\"\n          >\n            <PlayIcon className=\"w-5 h-5\" />\n            Watch Demo\n          </Link>\n        </div>\n        \n        {/* Statistics */}\n        <div ref={statsRef} className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-blue-400 mb-2\">15K+</div>\n            <div className=\"text-gray-400 text-sm\">Reports Submitted</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-purple-400 mb-2\">12K+</div>\n            <div className=\"text-gray-400 text-sm\">Cases Resolved</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-cyan-400 mb-2\">2.3h</div>\n            <div className=\"text-gray-400 text-sm\">Avg Response</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-green-400 mb-2\">98%</div>\n            <div className=\"text-gray-400 text-sm\">User Satisfaction</div>\n          </div>\n        </div>\n        \n        {/* Mini Demo Preview */}\n        <div className=\"mt-16 max-w-2xl mx-auto\">\n          <div className=\"glass rounded-2xl p-6 border border-blue-500/20\">\n            <h3 className=\"text-xl font-semibold mb-4 text-center\">Quick Report Preview</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n              <div className=\"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold\">1</div>\n                <div>\n                  <div className=\"font-medium\">Select Offense</div>\n                  <div className=\"text-gray-400\">Traffic, Public, Crime</div>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg\">\n                <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold\">2</div>\n                <div>\n                  <div className=\"font-medium\">Add Evidence</div>\n                  <div className=\"text-gray-400\">Photos, Videos, Audio</div>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg\">\n                <div className=\"w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold\">3</div>\n                <div>\n                  <div className=\"font-medium\">Track Progress</div>\n                  <div className=\"text-gray-400\">Real-time Updates</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n        <div className=\"animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ;YAEZ,MAAM;4CAAY;oBAChB,IAAI,aAAkB,eAAe,SAAS,OAAO,EAAE;wBACrD,IAAI;4BACF,MAAM,QAAQ,CAAC,2IAAqB,EAAE,OAAO;4BAC7C,MAAM,QAAQ,CAAC,oJAAqB,EAAE,OAAO;4BAE7C,QAAQ,MAAM,KAAK,CAAC;gCAClB,IAAI,SAAS,OAAO;gCACpB,OAAO;gCACP,eAAe;gCACf,eAAe;gCACf,cAAc;gCACd,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,aAAa;gCACb,iBAAiB;gCACjB,QAAQ;gCACR,QAAQ;gCACR,WAAW;gCACX,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,YAAY;gCACZ,WAAW;gCACX,UAAU;gCACV,UAAU;4BACZ;4BAEA,eAAe;wBACjB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,2BAA2B;wBAC3C;oBACF;gBACF;;YAEA;YAEA;kCAAO;oBACL,IAAI,OAAO,MAAM,OAAO;gBAC1B;;QACF;yBAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,wCAAmC;gBACjC,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;oBAAE,OAAO;gBAAI;gBAEtC,qBAAqB;gBACrB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;oBAAC,SAAS,OAAO;oBAAE,YAAY,OAAO;oBAAE,OAAO,OAAO;oBAAE,SAAS,OAAO;iBAAC,EAAE;oBAClF,SAAS;oBACT,GAAG;gBACL;gBAEA,+BAA+B;gBAC/B,GAAG,EAAE,CAAC,SAAS,OAAO,EAAE;oBACtB,SAAS;oBACT,GAAG;oBACH,UAAU;oBACV,MAAM;gBACR,GACC,EAAE,CAAC,YAAY,OAAO,EAAE;oBACvB,SAAS;oBACT,GAAG;oBACH,UAAU;oBACV,MAAM;gBACR,GAAG,SACF,EAAE,CAAC,OAAO,OAAO,EAAE;oBAClB,SAAS;oBACT,GAAG;oBACH,UAAU;oBACV,MAAM;gBACR,GAAG,SACF,EAAE,CAAC,SAAS,OAAO,EAAE;oBACpB,SAAS;oBACT,GAAG;oBACH,UAAU;oBACV,MAAM;gBACR,GAAG;gBAEH,6CAA6C;gBAC7C,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE;oBACvB,GAAG,CAAC;oBACJ,UAAU;oBACV,MAAM;oBACN,QAAQ,CAAC;oBACT,MAAM;gBACR;YACF;QACF;yBAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC;QACtB,wCAAmC;YACjC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;gBACtB,OAAO,UAAU,OAAO;gBACxB,GAAG,UAAU,CAAC,IAAI;gBAClB,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;;;;;;0BAIzC,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,KAAK;gBAAS,WAAU;;kCAE3B,6LAAC;wBACC,KAAK;wBACL,WAAU;;4BACX;0CAEC,6LAAC;;;;;0CACD,6LAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;kCAI/B,6LAAC;wBACC,KAAK;wBACL,WAAU;kCACX;;;;;;kCAMD,6LAAC;wBAAI,KAAK;wBAAQ,WAAU;;0CAC1B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,cAAc,IAAM,eAAe;gCACnC,cAAc,IAAM,eAAe;;oCACpC;kDAEC,6LAAC,kOAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;;;;;;;0CAG9B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,KAAK;wBAAU,WAAU;;0CAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsD;;;;;;kDACrE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAyF;;;;;;8DACxG,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA2F;;;;;;8DAC1G,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAyF;;;;;;8DACxG,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAvOwB;KAAA", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/lib/constants.js"], "sourcesContent": ["// ReportU Application Constants\n\n// Application Information\nexport const APP_INFO = {\n  name: 'ReportU',\n  tagline: 'Cross-Border Offense Reporting Platform',\n  description: 'Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.',\n  version: '1.0.0',\n  author: 'ReportU Team',\n  website: 'https://reportu.vercel.app',\n  logo: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp'\n};\n\n// Countries and Authorities\nexport const COUNTRIES = {\n  MALAYSIA: 'malaysia',\n  SINGAPORE: 'singapore'\n};\n\nexport const AUTHORITIES = {\n  // Malaysia\n  JPJ: {\n    id: 'jpj',\n    name: '<PERSON><PERSON> (Road Transport Department)',\n    country: COUNTRIES.MALAYSIA,\n    types: ['traffic', 'vehicle'],\n    contact: '+603-8888-1000',\n    website: 'https://www.jpj.gov.my'\n  },\n  PDRM: {\n    id: 'pdrm',\n    name: '<PERSON>R<PERSON> (Royal Malaysia Police)',\n    country: COUNTRIES.MALAYSIA,\n    types: ['crime', 'public', 'emergency'],\n    contact: '999',\n    website: 'https://www.rmp.gov.my'\n  },\n  KPDNHEP: {\n    id: 'kpdnhep',\n    name: 'KPDNHEP (Consumer Protection)',\n    country: COUNTRIES.MALAYSIA,\n    types: ['consumer', 'counterfeit'],\n    contact: '+603-8882-5555',\n    website: 'https://www.kpdnhep.gov.my'\n  },\n  \n  // Singapore\n  LTA: {\n    id: 'lta',\n    name: 'LTA (Land Transport Authority)',\n    country: COUNTRIES.SINGAPORE,\n    types: ['traffic', 'vehicle'],\n    contact: '+65-6225-5582',\n    website: 'https://www.lta.gov.sg'\n  },\n  SPF: {\n    id: 'spf',\n    name: 'SPF (Singapore Police Force)',\n    country: COUNTRIES.SINGAPORE,\n    types: ['crime', 'public', 'emergency'],\n    contact: '999',\n    website: 'https://www.police.gov.sg'\n  },\n  CASE: {\n    id: 'case',\n    name: 'CASE (Consumer Protection)',\n    country: COUNTRIES.SINGAPORE,\n    types: ['consumer', 'counterfeit'],\n    contact: '+65-6100-0315',\n    website: 'https://www.case.org.sg'\n  }\n};\n\n// Offense Types\nexport const OFFENSE_TYPES = {\n  TRAFFIC: {\n    id: 'traffic',\n    name: 'Traffic Violations',\n    icon: '🚗',\n    description: 'Speeding, illegal parking, running red lights, reckless driving',\n    examples: ['Speeding', 'Illegal Parking', 'Red Light Violation', 'Reckless Driving', 'Lane Cutting'],\n    authorities: {\n      [COUNTRIES.MALAYSIA]: ['jpj', 'pdrm'],\n      [COUNTRIES.SINGAPORE]: ['lta', 'spf']\n    }\n  },\n  PUBLIC: {\n    id: 'public',\n    name: 'Public Disturbances',\n    icon: '🏛️',\n    description: 'Noise complaints, littering, vandalism, public safety issues',\n    examples: ['Noise Complaint', 'Littering', 'Vandalism', 'Illegal Dumping', 'Public Safety'],\n    authorities: {\n      [COUNTRIES.MALAYSIA]: ['pdrm'],\n      [COUNTRIES.SINGAPORE]: ['spf']\n    }\n  },\n  CONSUMER: {\n    id: 'consumer',\n    name: 'Consumer Protection',\n    icon: '🛡️',\n    description: 'Counterfeit goods, false advertising, unfair practices',\n    examples: ['Counterfeit Products', 'False Advertising', 'Unfair Pricing', 'Poor Service', 'Scams'],\n    authorities: {\n      [COUNTRIES.MALAYSIA]: ['kpdnhep'],\n      [COUNTRIES.SINGAPORE]: ['case']\n    }\n  },\n  CRIME: {\n    id: 'crime',\n    name: 'Criminal Activities',\n    icon: '⚖️',\n    description: 'Theft, assault, fraud, drug-related offenses',\n    examples: ['Theft', 'Assault', 'Fraud', 'Drug Activity', 'Harassment'],\n    authorities: {\n      [COUNTRIES.MALAYSIA]: ['pdrm'],\n      [COUNTRIES.SINGAPORE]: ['spf']\n    }\n  },\n  OTHER: {\n    id: 'other',\n    name: 'Other Offenses',\n    icon: '📋',\n    description: 'Other violations not covered in specific categories',\n    examples: ['Environmental Issues', 'Building Violations', 'Health Code Violations'],\n    authorities: {\n      [COUNTRIES.MALAYSIA]: ['pdrm'],\n      [COUNTRIES.SINGAPORE]: ['spf']\n    }\n  }\n};\n\n// Report Status\nexport const REPORT_STATUS = {\n  SUBMITTED: {\n    id: 'submitted',\n    name: 'Submitted',\n    description: 'Report has been submitted and is being processed',\n    color: '#f59e0b',\n    icon: '📝'\n  },\n  PROCESSING: {\n    id: 'processing',\n    name: 'Processing',\n    description: 'Report is being reviewed by the appropriate authority',\n    color: '#3b82f6',\n    icon: '⏳'\n  },\n  INVESTIGATING: {\n    id: 'investigating',\n    name: 'Investigating',\n    description: 'Authority is actively investigating the reported offense',\n    color: '#8b5cf6',\n    icon: '🔍'\n  },\n  RESOLVED: {\n    id: 'resolved',\n    name: 'Resolved',\n    description: 'Report has been resolved and action has been taken',\n    color: '#10b981',\n    icon: '✅'\n  },\n  CLOSED: {\n    id: 'closed',\n    name: 'Closed',\n    description: 'Report has been closed (no action required or insufficient evidence)',\n    color: '#6b7280',\n    icon: '❌'\n  }\n};\n\n// Evidence Types\nexport const EVIDENCE_TYPES = {\n  IMAGE: {\n    id: 'image',\n    name: 'Photo',\n    extensions: ['.jpg', '.jpeg', '.png', '.webp'],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: '📸'\n  },\n  VIDEO: {\n    id: 'video',\n    name: 'Video',\n    extensions: ['.mp4', '.mov', '.avi', '.webm'],\n    maxSize: 50 * 1024 * 1024, // 50MB\n    icon: '🎥'\n  },\n  AUDIO: {\n    id: 'audio',\n    name: 'Audio',\n    extensions: ['.mp3', '.wav', '.m4a', '.ogg'],\n    maxSize: 20 * 1024 * 1024, // 20MB\n    icon: '🎵'\n  }\n};\n\n// Languages\nexport const LANGUAGES = {\n  EN: {\n    code: 'en',\n    name: 'English',\n    flag: '🇺🇸'\n  },\n  MS: {\n    code: 'ms',\n    name: 'Bahasa Malaysia',\n    flag: '🇲🇾'\n  },\n  ZH: {\n    code: 'zh',\n    name: '中文',\n    flag: '🇨🇳'\n  },\n  TA: {\n    code: 'ta',\n    name: 'தமிழ்',\n    flag: '🇮🇳'\n  }\n};\n\n// Navigation Links\nexport const NAV_LINKS = [\n  { href: '/', label: 'Home', icon: '🏠' },\n  { href: '/demo', label: 'Demo', icon: '🎮' },\n  { href: '/report', label: 'Report', icon: '📝' },\n  { href: '/dashboard', label: 'Dashboard', icon: '📊' },\n  { href: '/about', label: 'About', icon: 'ℹ️' }\n];\n\n// Social Links\nexport const SOCIAL_LINKS = {\n  twitter: 'https://twitter.com/reportu',\n  facebook: 'https://facebook.com/reportu',\n  linkedin: 'https://linkedin.com/company/reportu',\n  github: 'https://github.com/reportu'\n};\n\n// API Endpoints (Simulated)\nexport const API_ENDPOINTS = {\n  REPORTS: '/api/reports',\n  UPLOAD: '/api/upload',\n  AUTHORITIES: '/api/authorities',\n  STATISTICS: '/api/statistics'\n};\n\n// Animation Configurations\nexport const ANIMATIONS = {\n  DURATION: {\n    FAST: 0.2,\n    NORMAL: 0.3,\n    SLOW: 0.5,\n    EXTRA_SLOW: 1.0\n  },\n  EASING: {\n    EASE_OUT: 'ease-out',\n    EASE_IN_OUT: 'ease-in-out',\n    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'\n  }\n};\n\n// Breakpoints\nexport const BREAKPOINTS = {\n  SM: 640,\n  MD: 768,\n  LG: 1024,\n  XL: 1280,\n  '2XL': 1536\n};\n\n// Feature Flags\nexport const FEATURES = {\n  ANONYMOUS_REPORTING: true,\n  MULTI_LANGUAGE: true,\n  REAL_TIME_TRACKING: true,\n  COMMUNITY_DASHBOARD: true,\n  CROSS_BORDER: true,\n  AI_CATEGORIZATION: true\n};\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection and try again.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',\n  INVALID_FILE_TYPE: 'Invalid file type. Please select a supported file.',\n  LOCATION_ERROR: 'Unable to detect your location. Please enable location services.',\n  SUBMISSION_ERROR: 'Failed to submit report. Please try again later.'\n};\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  REPORT_SUBMITTED: 'Report submitted successfully! You will receive updates on the progress.',\n  FILE_UPLOADED: 'File uploaded successfully.',\n  LOCATION_DETECTED: 'Location detected successfully.'\n};\n\nexport default {\n  APP_INFO,\n  COUNTRIES,\n  AUTHORITIES,\n  OFFENSE_TYPES,\n  REPORT_STATUS,\n  EVIDENCE_TYPES,\n  LANGUAGES,\n  NAV_LINKS,\n  SOCIAL_LINKS,\n  API_ENDPOINTS,\n  ANIMATIONS,\n  BREAKPOINTS,\n  FEATURES,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES\n};\n"], "names": [], "mappings": "AAAA,gCAAgC;AAEhC,0BAA0B;;;;;;;;;;;;;;;;;;;AACnB,MAAM,WAAW;IACtB,MAAM;IACN,SAAS;IACT,aAAa;IACb,SAAS;IACT,QAAQ;IACR,SAAS;IACT,MAAM;AACR;AAGO,MAAM,YAAY;IACvB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,cAAc;IACzB,WAAW;IACX,KAAK;QACH,IAAI;QACJ,MAAM;QACN,SAAS,UAAU,QAAQ;QAC3B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;QACT,SAAS;IACX;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,SAAS,UAAU,QAAQ;QAC3B,OAAO;YAAC;YAAS;YAAU;SAAY;QACvC,SAAS;QACT,SAAS;IACX;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,SAAS,UAAU,QAAQ;QAC3B,OAAO;YAAC;YAAY;SAAc;QAClC,SAAS;QACT,SAAS;IACX;IAEA,YAAY;IACZ,KAAK;QACH,IAAI;QACJ,MAAM;QACN,SAAS,UAAU,SAAS;QAC5B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;QACT,SAAS;IACX;IACA,KAAK;QACH,IAAI;QACJ,MAAM;QACN,SAAS,UAAU,SAAS;QAC5B,OAAO;YAAC;YAAS;YAAU;SAAY;QACvC,SAAS;QACT,SAAS;IACX;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,SAAS,UAAU,SAAS;QAC5B,OAAO;YAAC;YAAY;SAAc;QAClC,SAAS;QACT,SAAS;IACX;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;QACP,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAY;YAAmB;YAAuB;YAAoB;SAAe;QACpG,aAAa;YACX,CAAC,UAAU,QAAQ,CAAC,EAAE;gBAAC;gBAAO;aAAO;YACrC,CAAC,UAAU,SAAS,CAAC,EAAE;gBAAC;gBAAO;aAAM;QACvC;IACF;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAmB;YAAa;YAAa;YAAmB;SAAgB;QAC3F,aAAa;YACX,CAAC,UAAU,QAAQ,CAAC,EAAE;gBAAC;aAAO;YAC9B,CAAC,UAAU,SAAS,CAAC,EAAE;gBAAC;aAAM;QAChC;IACF;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAwB;YAAqB;YAAkB;YAAgB;SAAQ;QAClG,aAAa;YACX,CAAC,UAAU,QAAQ,CAAC,EAAE;gBAAC;aAAU;YACjC,CAAC,UAAU,SAAS,CAAC,EAAE;gBAAC;aAAO;QACjC;IACF;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAS;YAAW;YAAS;YAAiB;SAAa;QACtE,aAAa;YACX,CAAC,UAAU,QAAQ,CAAC,EAAE;gBAAC;aAAO;YAC9B,CAAC,UAAU,SAAS,CAAC,EAAE;gBAAC;aAAM;QAChC;IACF;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAwB;YAAuB;SAAyB;QACnF,aAAa;YACX,CAAC,UAAU,QAAQ,CAAC,EAAE;gBAAC;aAAO;YAC9B,CAAC,UAAU,SAAS,CAAC,EAAE;gBAAC;aAAM;QAChC;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,WAAW;QACT,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA,eAAe;QACb,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB;IAC5B,OAAO;QACL,IAAI;QACJ,MAAM;QACN,YAAY;YAAC;YAAQ;YAAS;YAAQ;SAAQ;QAC9C,SAAS,KAAK,OAAO;QACrB,MAAM;IACR;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,YAAY;YAAC;YAAQ;YAAQ;YAAQ;SAAQ;QAC7C,SAAS,KAAK,OAAO;QACrB,MAAM;IACR;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,YAAY;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC5C,SAAS,KAAK,OAAO;QACrB,MAAM;IACR;AACF;AAGO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,IAAI;QACF,MAAM;QACN,MAAM;QACN,MAAM;IACR;AACF;AAGO,MAAM,YAAY;IACvB;QAAE,MAAM;QAAK,OAAO;QAAQ,MAAM;IAAK;IACvC;QAAE,MAAM;QAAS,OAAO;QAAQ,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAW,OAAO;QAAU,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAc,OAAO;QAAa,MAAM;IAAK;IACrD;QAAE,MAAM;QAAU,OAAO;QAAS,MAAM;IAAK;CAC9C;AAGM,MAAM,eAAe;IAC1B,SAAS;IACT,UAAU;IACV,UAAU;IACV,QAAQ;AACV;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,QAAQ;IACR,aAAa;IACb,YAAY;AACd;AAGO,MAAM,aAAa;IACxB,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,YAAY;IACd;IACA,QAAQ;QACN,UAAU;QACV,aAAa;QACb,QAAQ;IACV;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,qBAAqB;IACrB,gBAAgB;IAChB,oBAAoB;IACpB,qBAAqB;IACrB,cAAc;IACd,mBAAmB;AACrB;AAGO,MAAM,iBAAiB;IAC5B,eAAe;IACf,kBAAkB;IAClB,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,kBAAkB;AACpB;AAGO,MAAM,mBAAmB;IAC9B,kBAAkB;IAClB,eAAe;IACf,mBAAmB;AACrB;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/lib/utils.js"], "sourcesContent": ["// ReportU Utility Functions\n\nimport { AUTHORITIES, OFFENSE_TYPES, COUNTRIES, EVIDENCE_TYPES } from './constants';\n\n/**\n * Utility function to combine class names\n * @param {...string} classes - Class names to combine\n * @returns {string} Combined class names\n */\nexport function cn(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\n/**\n * Generate a unique ID\n * @returns {string} Unique ID\n */\nexport function generateId() {\n  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Format date to readable string\n * @param {Date|string} date - Date to format\n * @returns {string} Formatted date\n */\nexport function formatDate(date) {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\n/**\n * Format file size to human readable format\n * @param {number} bytes - File size in bytes\n * @returns {string} Formatted file size\n */\nexport function formatFileSize(bytes) {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n/**\n * Validate file type and size\n * @param {File} file - File to validate\n * @returns {Object} Validation result\n */\nexport function validateFile(file) {\n  const errors = [];\n  \n  // Determine file type\n  let fileType = null;\n  const extension = '.' + file.name.split('.').pop().toLowerCase();\n  \n  for (const [type, config] of Object.entries(EVIDENCE_TYPES)) {\n    if (config.extensions.includes(extension)) {\n      fileType = type.toLowerCase();\n      break;\n    }\n  }\n  \n  if (!fileType) {\n    errors.push(`Unsupported file type: ${extension}`);\n  } else {\n    // Check file size\n    const maxSize = EVIDENCE_TYPES[fileType.toUpperCase()].maxSize;\n    if (file.size > maxSize) {\n      errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`);\n    }\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n    fileType\n  };\n}\n\n/**\n * Get appropriate authorities for offense type and country\n * @param {string} offenseType - Type of offense\n * @param {string} country - Country code\n * @returns {Array} Array of authority objects\n */\nexport function getAuthoritiesForOffense(offenseType, country) {\n  const offense = OFFENSE_TYPES[offenseType.toUpperCase()];\n  if (!offense || !offense.authorities[country]) {\n    return [];\n  }\n  \n  return offense.authorities[country].map(authorityId => \n    AUTHORITIES[authorityId.toUpperCase()]\n  ).filter(Boolean);\n}\n\n/**\n * Detect country from coordinates\n * @param {number} lat - Latitude\n * @param {number} lng - Longitude\n * @returns {string} Country code\n */\nexport function detectCountryFromCoordinates(lat, lng) {\n  // Malaysia bounds (approximate)\n  const malaysiaBounds = {\n    north: 7.5,\n    south: 0.8,\n    east: 119.5,\n    west: 99.5\n  };\n  \n  // Singapore bounds (approximate)\n  const singaporeBounds = {\n    north: 1.5,\n    south: 1.2,\n    east: 104.1,\n    west: 103.6\n  };\n  \n  // Check Singapore first (smaller area)\n  if (lat >= singaporeBounds.south && lat <= singaporeBounds.north &&\n      lng >= singaporeBounds.west && lng <= singaporeBounds.east) {\n    return COUNTRIES.SINGAPORE;\n  }\n  \n  // Check Malaysia\n  if (lat >= malaysiaBounds.south && lat <= malaysiaBounds.north &&\n      lng >= malaysiaBounds.west && lng <= malaysiaBounds.east) {\n    return COUNTRIES.MALAYSIA;\n  }\n  \n  // Default to Malaysia if coordinates are unclear\n  return COUNTRIES.MALAYSIA;\n}\n\n/**\n * Get user's current location\n * @returns {Promise<Object>} Location object with lat, lng, and country\n */\nexport function getCurrentLocation() {\n  return new Promise((resolve, reject) => {\n    if (!navigator.geolocation) {\n      reject(new Error('Geolocation is not supported by this browser'));\n      return;\n    }\n    \n    navigator.geolocation.getCurrentPosition(\n      (position) => {\n        const lat = position.coords.latitude;\n        const lng = position.coords.longitude;\n        const country = detectCountryFromCoordinates(lat, lng);\n        \n        resolve({\n          lat,\n          lng,\n          country,\n          accuracy: position.coords.accuracy\n        });\n      },\n      (error) => {\n        reject(error);\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      }\n    );\n  });\n}\n\n/**\n * Debounce function\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function\n * @param {Function} func - Function to throttle\n * @param {number} limit - Time limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function() {\n    const args = arguments;\n    const context = this;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Check if device is mobile\n * @returns {boolean} True if mobile device\n */\nexport function isMobile() {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n/**\n * Copy text to clipboard\n * @param {string} text - Text to copy\n * @returns {Promise<boolean>} Success status\n */\nexport async function copyToClipboard(text) {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (err) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n/**\n * Generate random color\n * @returns {string} Random hex color\n */\nexport function getRandomColor() {\n  const colors = [\n    '#3b82f6', '#8b5cf6', '#06b6d4', '#10b981', \n    '#f59e0b', '#ef4444', '#ec4899', '#84cc16'\n  ];\n  return colors[Math.floor(Math.random() * colors.length)];\n}\n\n/**\n * Calculate distance between two coordinates\n * @param {number} lat1 - First latitude\n * @param {number} lng1 - First longitude\n * @param {number} lat2 - Second latitude\n * @param {number} lng2 - Second longitude\n * @returns {number} Distance in kilometers\n */\nexport function calculateDistance(lat1, lng1, lat2, lng2) {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = (lat2 - lat1) * Math.PI / 180;\n  const dLng = (lng2 - lng1) * Math.PI / 180;\n  const a = \n    Math.sin(dLat/2) * Math.sin(dLat/2) +\n    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n    Math.sin(dLng/2) * Math.sin(dLng/2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n  return R * c;\n}\n\n/**\n * Format address from coordinates (mock implementation)\n * @param {number} lat - Latitude\n * @param {number} lng - Longitude\n * @returns {Promise<string>} Formatted address\n */\nexport async function formatAddress(lat, lng) {\n  // Mock address formatting - in real app, use reverse geocoding API\n  const country = detectCountryFromCoordinates(lat, lng);\n  const areas = {\n    [COUNTRIES.MALAYSIA]: ['Kuala Lumpur', 'Selangor', 'Penang', 'Johor', 'Perak'],\n    [COUNTRIES.SINGAPORE]: ['Central', 'North', 'South', 'East', 'West']\n  };\n  \n  const randomArea = areas[country][Math.floor(Math.random() * areas[country].length)];\n  return `${randomArea}, ${country === COUNTRIES.MALAYSIA ? 'Malaysia' : 'Singapore'}`;\n}\n\n/**\n * Validate email address\n * @param {string} email - Email to validate\n * @returns {boolean} True if valid email\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Validate phone number\n * @param {string} phone - Phone number to validate\n * @returns {boolean} True if valid phone\n */\nexport function isValidPhone(phone) {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n  return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n}\n\n/**\n * Generate report reference number\n * @param {string} country - Country code\n * @param {string} offenseType - Offense type\n * @returns {string} Report reference number\n */\nexport function generateReportReference(country, offenseType) {\n  const countryCode = country === COUNTRIES.MALAYSIA ? 'MY' : 'SG';\n  const typeCode = offenseType.substring(0, 3).toUpperCase();\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 3).toUpperCase();\n  \n  return `${countryCode}-${typeCode}-${timestamp}-${random}`;\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;;;;;;;;;;AAE5B;;AAOO,SAAS,GAAG,GAAG,OAAO;IAC3B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAMO,SAAS;IACd,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AACnE;AAOO,SAAS,WAAW,IAAI;IAC7B,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAOO,SAAS,eAAe,KAAK;IAClC,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAOO,SAAS,aAAa,IAAI;IAC/B,MAAM,SAAS,EAAE;IAEjB,sBAAsB;IACtB,IAAI,WAAW;IACf,MAAM,YAAY,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,WAAW;IAE9D,KAAK,MAAM,CAAC,MAAM,OAAO,IAAI,OAAO,OAAO,CAAC,0HAAA,CAAA,iBAAc,EAAG;QAC3D,IAAI,OAAO,UAAU,CAAC,QAAQ,CAAC,YAAY;YACzC,WAAW,KAAK,WAAW;YAC3B;QACF;IACF;IAEA,IAAI,CAAC,UAAU;QACb,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,WAAW;IACnD,OAAO;QACL,kBAAkB;QAClB,MAAM,UAAU,0HAAA,CAAA,iBAAc,CAAC,SAAS,WAAW,GAAG,CAAC,OAAO;QAC9D,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,eAAe,KAAK,IAAI,EAAE,gCAAgC,EAAE,eAAe,SAAS,CAAC,CAAC;QAClH;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;IACF;AACF;AAQO,SAAS,yBAAyB,WAAW,EAAE,OAAO;IAC3D,MAAM,UAAU,0HAAA,CAAA,gBAAa,CAAC,YAAY,WAAW,GAAG;IACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,WAAW,CAAC,QAAQ,EAAE;QAC7C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,cACtC,0HAAA,CAAA,cAAW,CAAC,YAAY,WAAW,GAAG,EACtC,MAAM,CAAC;AACX;AAQO,SAAS,6BAA6B,GAAG,EAAE,GAAG;IACnD,gCAAgC;IAChC,MAAM,iBAAiB;QACrB,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,iCAAiC;IACjC,MAAM,kBAAkB;QACtB,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,uCAAuC;IACvC,IAAI,OAAO,gBAAgB,KAAK,IAAI,OAAO,gBAAgB,KAAK,IAC5D,OAAO,gBAAgB,IAAI,IAAI,OAAO,gBAAgB,IAAI,EAAE;QAC9D,OAAO,0HAAA,CAAA,YAAS,CAAC,SAAS;IAC5B;IAEA,iBAAiB;IACjB,IAAI,OAAO,eAAe,KAAK,IAAI,OAAO,eAAe,KAAK,IAC1D,OAAO,eAAe,IAAI,IAAI,OAAO,eAAe,IAAI,EAAE;QAC5D,OAAO,0HAAA,CAAA,YAAS,CAAC,QAAQ;IAC3B;IAEA,iDAAiD;IACjD,OAAO,0HAAA,CAAA,YAAS,CAAC,QAAQ;AAC3B;AAMO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;YACC,MAAM,MAAM,SAAS,MAAM,CAAC,QAAQ;YACpC,MAAM,MAAM,SAAS,MAAM,CAAC,SAAS;YACrC,MAAM,UAAU,6BAA6B,KAAK;YAElD,QAAQ;gBACN;gBACA;gBACA;gBACA,UAAU,SAAS,MAAM,CAAC,QAAQ;YACpC;QACF,GACA,CAAC;YACC,OAAO;QACT,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY,OAAO,YAAY;QACjC;IAEJ;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO;QACL,MAAM,OAAO;QACb,MAAM,UAAU,IAAI;QACpB,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,SAAS;YACpB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAMO,SAAS;IACd,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAOO,eAAe,gBAAgB,IAAI;IACxC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAMO,SAAS;IACd,MAAM,SAAS;QACb;QAAW;QAAW;QAAW;QACjC;QAAW;QAAW;QAAW;KAClC;IACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAUO,SAAS,kBAAkB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IACtD,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAC3D,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;IACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;IACnD,OAAO,IAAI;AACb;AAQO,eAAe,cAAc,GAAG,EAAE,GAAG;IAC1C,mEAAmE;IACnE,MAAM,UAAU,6BAA6B,KAAK;IAClD,MAAM,QAAQ;QACZ,CAAC,0HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,EAAE;YAAC;YAAgB;YAAY;YAAU;YAAS;SAAQ;QAC9E,CAAC,0HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,EAAE;YAAC;YAAW;YAAS;YAAS;YAAQ;SAAO;IACtE;IAEA,MAAM,aAAa,KAAK,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;IACpF,OAAO,GAAG,WAAW,EAAE,EAAE,YAAY,0HAAA,CAAA,YAAS,CAAC,QAAQ,GAAG,aAAa,aAAa;AACtF;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;AACtD;AAQO,SAAS,wBAAwB,OAAO,EAAE,WAAW;IAC1D,MAAM,cAAc,YAAY,0HAAA,CAAA,YAAS,CAAC,QAAQ,GAAG,OAAO;IAC5D,MAAM,WAAW,YAAY,SAAS,CAAC,GAAG,GAAG,WAAW;IACxD,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAElE,OAAO,GAAG,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC5D", "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/lib/storage.js"], "sourcesContent": ["// ReportU Storage Utilities - Simulated Backend using LocalStorage\n\nimport { generateId, generateReportReference } from './utils';\nimport { REPORT_STATUS } from './constants';\n\n// Storage Keys\nconst STORAGE_KEYS = {\n  REPORTS: 'reportu_reports',\n  USER_DATA: 'reportu_user_data',\n  STATISTICS: 'reportu_statistics',\n  SETTINGS: 'reportu_settings'\n};\n\n/**\n * Generic storage utilities\n */\nexport const storage = {\n  /**\n   * Get item from localStorage\n   * @param {string} key - Storage key\n   * @param {*} defaultValue - Default value if key doesn't exist\n   * @returns {*} Stored value or default\n   */\n  get(key, defaultValue = null) {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue;\n    } catch (error) {\n      console.error('Error reading from localStorage:', error);\n      return defaultValue;\n    }\n  },\n\n  /**\n   * Set item in localStorage\n   * @param {string} key - Storage key\n   * @param {*} value - Value to store\n   */\n  set(key, value) {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Error writing to localStorage:', error);\n    }\n  },\n\n  /**\n   * Remove item from localStorage\n   * @param {string} key - Storage key\n   */\n  remove(key) {\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error removing from localStorage:', error);\n    }\n  },\n\n  /**\n   * Clear all storage\n   */\n  clear() {\n    try {\n      Object.values(STORAGE_KEYS).forEach(key => {\n        localStorage.removeItem(key);\n      });\n    } catch (error) {\n      console.error('Error clearing localStorage:', error);\n    }\n  }\n};\n\n/**\n * Report storage utilities\n */\nexport const reportStorage = {\n  /**\n   * Get all reports\n   * @returns {Array} Array of reports\n   */\n  getAll() {\n    return storage.get(STORAGE_KEYS.REPORTS, []);\n  },\n\n  /**\n   * Get report by ID\n   * @param {string} id - Report ID\n   * @returns {Object|null} Report object or null\n   */\n  getById(id) {\n    const reports = this.getAll();\n    return reports.find(report => report.id === id) || null;\n  },\n\n  /**\n   * Save new report\n   * @param {Object} reportData - Report data\n   * @returns {Object} Saved report with generated ID\n   */\n  save(reportData) {\n    const reports = this.getAll();\n    const newReport = {\n      id: generateId(),\n      reference: generateReportReference(reportData.location.country, reportData.type),\n      ...reportData,\n      status: REPORT_STATUS.SUBMITTED.id,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      timeline: [\n        {\n          status: REPORT_STATUS.SUBMITTED.id,\n          timestamp: new Date().toISOString(),\n          message: 'Report submitted successfully'\n        }\n      ]\n    };\n\n    reports.push(newReport);\n    storage.set(STORAGE_KEYS.REPORTS, reports);\n    \n    // Update statistics\n    this.updateStatistics();\n    \n    return newReport;\n  },\n\n  /**\n   * Update report\n   * @param {string} id - Report ID\n   * @param {Object} updates - Updates to apply\n   * @returns {Object|null} Updated report or null\n   */\n  update(id, updates) {\n    const reports = this.getAll();\n    const index = reports.findIndex(report => report.id === id);\n    \n    if (index === -1) return null;\n    \n    const updatedReport = {\n      ...reports[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    \n    // Add timeline entry if status changed\n    if (updates.status && updates.status !== reports[index].status) {\n      updatedReport.timeline = [\n        ...reports[index].timeline,\n        {\n          status: updates.status,\n          timestamp: new Date().toISOString(),\n          message: updates.statusMessage || `Status changed to ${updates.status}`\n        }\n      ];\n    }\n    \n    reports[index] = updatedReport;\n    storage.set(STORAGE_KEYS.REPORTS, reports);\n    \n    return updatedReport;\n  },\n\n  /**\n   * Delete report\n   * @param {string} id - Report ID\n   * @returns {boolean} Success status\n   */\n  delete(id) {\n    const reports = this.getAll();\n    const filteredReports = reports.filter(report => report.id !== id);\n    \n    if (filteredReports.length === reports.length) {\n      return false; // Report not found\n    }\n    \n    storage.set(STORAGE_KEYS.REPORTS, filteredReports);\n    this.updateStatistics();\n    return true;\n  },\n\n  /**\n   * Get reports by filters\n   * @param {Object} filters - Filter criteria\n   * @returns {Array} Filtered reports\n   */\n  getByFilters(filters = {}) {\n    const reports = this.getAll();\n    \n    return reports.filter(report => {\n      // Filter by status\n      if (filters.status && report.status !== filters.status) {\n        return false;\n      }\n      \n      // Filter by type\n      if (filters.type && report.type !== filters.type) {\n        return false;\n      }\n      \n      // Filter by country\n      if (filters.country && report.location.country !== filters.country) {\n        return false;\n      }\n      \n      // Filter by date range\n      if (filters.startDate) {\n        const reportDate = new Date(report.createdAt);\n        const startDate = new Date(filters.startDate);\n        if (reportDate < startDate) return false;\n      }\n      \n      if (filters.endDate) {\n        const reportDate = new Date(report.createdAt);\n        const endDate = new Date(filters.endDate);\n        if (reportDate > endDate) return false;\n      }\n      \n      return true;\n    });\n  },\n\n  /**\n   * Update statistics\n   */\n  updateStatistics() {\n    const reports = this.getAll();\n    const stats = {\n      total: reports.length,\n      byStatus: {},\n      byType: {},\n      byCountry: {},\n      lastUpdated: new Date().toISOString()\n    };\n    \n    reports.forEach(report => {\n      // Count by status\n      stats.byStatus[report.status] = (stats.byStatus[report.status] || 0) + 1;\n      \n      // Count by type\n      stats.byType[report.type] = (stats.byType[report.type] || 0) + 1;\n      \n      // Count by country\n      stats.byCountry[report.location.country] = (stats.byCountry[report.location.country] || 0) + 1;\n    });\n    \n    storage.set(STORAGE_KEYS.STATISTICS, stats);\n  }\n};\n\n/**\n * User data storage utilities\n */\nexport const userStorage = {\n  /**\n   * Get user data\n   * @returns {Object} User data\n   */\n  get() {\n    return storage.get(STORAGE_KEYS.USER_DATA, {\n      preferences: {\n        language: 'en',\n        notifications: true,\n        anonymousReporting: false\n      },\n      profile: {\n        name: '',\n        email: '',\n        phone: ''\n      }\n    });\n  },\n\n  /**\n   * Save user data\n   * @param {Object} userData - User data to save\n   */\n  save(userData) {\n    const currentData = this.get();\n    const updatedData = {\n      ...currentData,\n      ...userData,\n      lastUpdated: new Date().toISOString()\n    };\n    storage.set(STORAGE_KEYS.USER_DATA, updatedData);\n  },\n\n  /**\n   * Update user preferences\n   * @param {Object} preferences - Preferences to update\n   */\n  updatePreferences(preferences) {\n    const userData = this.get();\n    userData.preferences = {\n      ...userData.preferences,\n      ...preferences\n    };\n    this.save(userData);\n  },\n\n  /**\n   * Update user profile\n   * @param {Object} profile - Profile data to update\n   */\n  updateProfile(profile) {\n    const userData = this.get();\n    userData.profile = {\n      ...userData.profile,\n      ...profile\n    };\n    this.save(userData);\n  }\n};\n\n/**\n * Settings storage utilities\n */\nexport const settingsStorage = {\n  /**\n   * Get settings\n   * @returns {Object} Settings\n   */\n  get() {\n    return storage.get(STORAGE_KEYS.SETTINGS, {\n      theme: 'dark',\n      animations: true,\n      autoLocation: true,\n      notifications: true\n    });\n  },\n\n  /**\n   * Save settings\n   * @param {Object} settings - Settings to save\n   */\n  save(settings) {\n    const currentSettings = this.get();\n    const updatedSettings = {\n      ...currentSettings,\n      ...settings,\n      lastUpdated: new Date().toISOString()\n    };\n    storage.set(STORAGE_KEYS.SETTINGS, updatedSettings);\n  }\n};\n\n/**\n * Statistics utilities\n */\nexport const statisticsStorage = {\n  /**\n   * Get statistics\n   * @returns {Object} Statistics\n   */\n  get() {\n    return storage.get(STORAGE_KEYS.STATISTICS, {\n      total: 0,\n      byStatus: {},\n      byType: {},\n      byCountry: {},\n      lastUpdated: new Date().toISOString()\n    });\n  },\n\n  /**\n   * Get community statistics (mock data for demo)\n   * @returns {Object} Community statistics\n   */\n  getCommunityStats() {\n    const baseStats = this.get();\n    \n    // Add some mock community data for demo purposes\n    return {\n      ...baseStats,\n      totalUsers: 12847,\n      reportsThisMonth: 1234,\n      resolvedThisMonth: 987,\n      averageResponseTime: '2.3 hours',\n      topReportTypes: [\n        { type: 'traffic', count: 456, percentage: 37 },\n        { type: 'public', count: 321, percentage: 26 },\n        { type: 'consumer', count: 234, percentage: 19 },\n        { type: 'crime', count: 123, percentage: 10 },\n        { type: 'other', count: 100, percentage: 8 }\n      ],\n      recentActivity: [\n        { id: '1', type: 'traffic', location: 'Kuala Lumpur', time: '2 minutes ago' },\n        { id: '2', type: 'public', location: 'Singapore', time: '5 minutes ago' },\n        { id: '3', type: 'consumer', location: 'Penang', time: '8 minutes ago' }\n      ]\n    };\n  }\n};\n\n/**\n * Initialize demo data\n */\nexport function initializeDemoData() {\n  const reports = reportStorage.getAll();\n  \n  // Only initialize if no reports exist\n  if (reports.length === 0) {\n    const demoReports = [\n      {\n        type: 'traffic',\n        title: 'Illegal Parking',\n        description: 'Car parked in disabled parking space without permit',\n        location: {\n          country: 'malaysia',\n          coordinates: { lat: 3.1390, lng: 101.6869 },\n          address: 'Kuala Lumpur, Malaysia'\n        },\n        evidence: [\n          { type: 'image', url: '/demo/parking-violation.jpg', timestamp: new Date().toISOString() }\n        ],\n        anonymous: false,\n        reporter: {\n          name: 'Ahmad Rahman',\n          email: '<EMAIL>'\n        }\n      },\n      {\n        type: 'public',\n        title: 'Noise Complaint',\n        description: 'Loud music from construction site after 10 PM',\n        location: {\n          country: 'singapore',\n          coordinates: { lat: 1.3521, lng: 103.8198 },\n          address: 'Central, Singapore'\n        },\n        evidence: [\n          { type: 'audio', url: '/demo/noise-complaint.mp3', timestamp: new Date().toISOString() }\n        ],\n        anonymous: true\n      }\n    ];\n    \n    demoReports.forEach(report => reportStorage.save(report));\n  }\n}\n\nexport default {\n  storage,\n  reportStorage,\n  userStorage,\n  settingsStorage,\n  statisticsStorage,\n  initializeDemoData\n};\n"], "names": [], "mappings": "AAAA,mEAAmE;;;;;;;;;;AAEnE;AACA;;;AAEA,eAAe;AACf,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,YAAY;IACZ,UAAU;AACZ;AAKO,MAAM,UAAU;IACrB;;;;;GAKC,GACD,KAAI,GAAG,EAAE,eAAe,IAAI;QAC1B,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,KAAI,GAAG,EAAE,KAAK;QACZ,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA;;;GAGC,GACD,QAAO,GAAG;QACR,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD;QACE,IAAI;YACF,OAAO,MAAM,CAAC,cAAc,OAAO,CAAC,CAAA;gBAClC,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;AACF;AAKO,MAAM,gBAAgB;IAC3B;;;GAGC,GACD;QACE,OAAO,QAAQ,GAAG,CAAC,aAAa,OAAO,EAAE,EAAE;IAC7C;IAEA;;;;GAIC,GACD,SAAQ,EAAE;QACR,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,OAAO;IACrD;IAEA;;;;GAIC,GACD,MAAK,UAAU;QACb,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,MAAM,YAAY;YAChB,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;YACb,WAAW,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW,QAAQ,CAAC,OAAO,EAAE,WAAW,IAAI;YAC/E,GAAG,UAAU;YACb,QAAQ,0HAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,EAAE;YAClC,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;gBACR;oBACE,QAAQ,0HAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,EAAE;oBAClC,WAAW,IAAI,OAAO,WAAW;oBACjC,SAAS;gBACX;aACD;QACH;QAEA,QAAQ,IAAI,CAAC;QACb,QAAQ,GAAG,CAAC,aAAa,OAAO,EAAE;QAElC,oBAAoB;QACpB,IAAI,CAAC,gBAAgB;QAErB,OAAO;IACT;IAEA;;;;;GAKC,GACD,QAAO,EAAE,EAAE,OAAO;QAChB,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAExD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,gBAAgB;YACpB,GAAG,OAAO,CAAC,MAAM;YACjB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,uCAAuC;QACvC,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;YAC9D,cAAc,QAAQ,GAAG;mBACpB,OAAO,CAAC,MAAM,CAAC,QAAQ;gBAC1B;oBACE,QAAQ,QAAQ,MAAM;oBACtB,WAAW,IAAI,OAAO,WAAW;oBACjC,SAAS,QAAQ,aAAa,IAAI,CAAC,kBAAkB,EAAE,QAAQ,MAAM,EAAE;gBACzE;aACD;QACH;QAEA,OAAO,CAAC,MAAM,GAAG;QACjB,QAAQ,GAAG,CAAC,aAAa,OAAO,EAAE;QAElC,OAAO;IACT;IAEA;;;;GAIC,GACD,QAAO,EAAE;QACP,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAE/D,IAAI,gBAAgB,MAAM,KAAK,QAAQ,MAAM,EAAE;YAC7C,OAAO,OAAO,mBAAmB;QACnC;QAEA,QAAQ,GAAG,CAAC,aAAa,OAAO,EAAE;QAClC,IAAI,CAAC,gBAAgB;QACrB,OAAO;IACT;IAEA;;;;GAIC,GACD,cAAa,UAAU,CAAC,CAAC;QACvB,MAAM,UAAU,IAAI,CAAC,MAAM;QAE3B,OAAO,QAAQ,MAAM,CAAC,CAAA;YACpB,mBAAmB;YACnB,IAAI,QAAQ,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,MAAM,EAAE;gBACtD,OAAO;YACT;YAEA,iBAAiB;YACjB,IAAI,QAAQ,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,EAAE;gBAChD,OAAO;YACT;YAEA,oBAAoB;YACpB,IAAI,QAAQ,OAAO,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,QAAQ,OAAO,EAAE;gBAClE,OAAO;YACT;YAEA,uBAAuB;YACvB,IAAI,QAAQ,SAAS,EAAE;gBACrB,MAAM,aAAa,IAAI,KAAK,OAAO,SAAS;gBAC5C,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS;gBAC5C,IAAI,aAAa,WAAW,OAAO;YACrC;YAEA,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,aAAa,IAAI,KAAK,OAAO,SAAS;gBAC5C,MAAM,UAAU,IAAI,KAAK,QAAQ,OAAO;gBACxC,IAAI,aAAa,SAAS,OAAO;YACnC;YAEA,OAAO;QACT;IACF;IAEA;;GAEC,GACD;QACE,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,MAAM,QAAQ;YACZ,OAAO,QAAQ,MAAM;YACrB,UAAU,CAAC;YACX,QAAQ,CAAC;YACT,WAAW,CAAC;YACZ,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,QAAQ,OAAO,CAAC,CAAA;YACd,kBAAkB;YAClB,MAAM,QAAQ,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI;YAEvE,gBAAgB;YAChB,MAAM,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;YAE/D,mBAAmB;YACnB,MAAM,SAAS,CAAC,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAC/F;QAEA,QAAQ,GAAG,CAAC,aAAa,UAAU,EAAE;IACvC;AACF;AAKO,MAAM,cAAc;IACzB;;;GAGC,GACD;QACE,OAAO,QAAQ,GAAG,CAAC,aAAa,SAAS,EAAE;YACzC,aAAa;gBACX,UAAU;gBACV,eAAe;gBACf,oBAAoB;YACtB;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;QACF;IACF;IAEA;;;GAGC,GACD,MAAK,QAAQ;QACX,MAAM,cAAc,IAAI,CAAC,GAAG;QAC5B,MAAM,cAAc;YAClB,GAAG,WAAW;YACd,GAAG,QAAQ;YACX,aAAa,IAAI,OAAO,WAAW;QACrC;QACA,QAAQ,GAAG,CAAC,aAAa,SAAS,EAAE;IACtC;IAEA;;;GAGC,GACD,mBAAkB,WAAW;QAC3B,MAAM,WAAW,IAAI,CAAC,GAAG;QACzB,SAAS,WAAW,GAAG;YACrB,GAAG,SAAS,WAAW;YACvB,GAAG,WAAW;QAChB;QACA,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA;;;GAGC,GACD,eAAc,OAAO;QACnB,MAAM,WAAW,IAAI,CAAC,GAAG;QACzB,SAAS,OAAO,GAAG;YACjB,GAAG,SAAS,OAAO;YACnB,GAAG,OAAO;QACZ;QACA,IAAI,CAAC,IAAI,CAAC;IACZ;AACF;AAKO,MAAM,kBAAkB;IAC7B;;;GAGC,GACD;QACE,OAAO,QAAQ,GAAG,CAAC,aAAa,QAAQ,EAAE;YACxC,OAAO;YACP,YAAY;YACZ,cAAc;YACd,eAAe;QACjB;IACF;IAEA;;;GAGC,GACD,MAAK,QAAQ;QACX,MAAM,kBAAkB,IAAI,CAAC,GAAG;QAChC,MAAM,kBAAkB;YACtB,GAAG,eAAe;YAClB,GAAG,QAAQ;YACX,aAAa,IAAI,OAAO,WAAW;QACrC;QACA,QAAQ,GAAG,CAAC,aAAa,QAAQ,EAAE;IACrC;AACF;AAKO,MAAM,oBAAoB;IAC/B;;;GAGC,GACD;QACE,OAAO,QAAQ,GAAG,CAAC,aAAa,UAAU,EAAE;YAC1C,OAAO;YACP,UAAU,CAAC;YACX,QAAQ,CAAC;YACT,WAAW,CAAC;YACZ,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA;;;GAGC,GACD;QACE,MAAM,YAAY,IAAI,CAAC,GAAG;QAE1B,iDAAiD;QACjD,OAAO;YACL,GAAG,SAAS;YACZ,YAAY;YACZ,kBAAkB;YAClB,mBAAmB;YACnB,qBAAqB;YACrB,gBAAgB;gBACd;oBAAE,MAAM;oBAAW,OAAO;oBAAK,YAAY;gBAAG;gBAC9C;oBAAE,MAAM;oBAAU,OAAO;oBAAK,YAAY;gBAAG;gBAC7C;oBAAE,MAAM;oBAAY,OAAO;oBAAK,YAAY;gBAAG;gBAC/C;oBAAE,MAAM;oBAAS,OAAO;oBAAK,YAAY;gBAAG;gBAC5C;oBAAE,MAAM;oBAAS,OAAO;oBAAK,YAAY;gBAAE;aAC5C;YACD,gBAAgB;gBACd;oBAAE,IAAI;oBAAK,MAAM;oBAAW,UAAU;oBAAgB,MAAM;gBAAgB;gBAC5E;oBAAE,IAAI;oBAAK,MAAM;oBAAU,UAAU;oBAAa,MAAM;gBAAgB;gBACxE;oBAAE,IAAI;oBAAK,MAAM;oBAAY,UAAU;oBAAU,MAAM;gBAAgB;aACxE;QACH;IACF;AACF;AAKO,SAAS;IACd,MAAM,UAAU,cAAc,MAAM;IAEpC,sCAAsC;IACtC,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,MAAM,cAAc;YAClB;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;oBACR,SAAS;oBACT,aAAa;wBAAE,KAAK;wBAAQ,KAAK;oBAAS;oBAC1C,SAAS;gBACX;gBACA,UAAU;oBACR;wBAAE,MAAM;wBAAS,KAAK;wBAA+B,WAAW,IAAI,OAAO,WAAW;oBAAG;iBAC1F;gBACD,WAAW;gBACX,UAAU;oBACR,MAAM;oBACN,OAAO;gBACT;YACF;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;oBACR,SAAS;oBACT,aAAa;wBAAE,KAAK;wBAAQ,KAAK;oBAAS;oBAC1C,SAAS;gBACX;gBACA,UAAU;oBACR;wBAAE,MAAM;wBAAS,KAAK;wBAA6B,WAAW,IAAI,OAAO,WAAW;oBAAG;iBACxF;gBACD,WAAW;YACb;SACD;QAED,YAAY,OAAO,CAAC,CAAA,SAAU,cAAc,IAAI,CAAC;IACnD;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 2111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport Hero from '@/components/sections/Hero';\nimport { initializeDemoData } from '@/lib/storage';\n\nexport default function Home() {\n  // Initialize demo data on first load\n  useEffect(() => {\n    initializeDemoData();\n  }, []);\n\n  return (\n    <main className=\"min-h-screen\">\n      <Hero />\n\n      {/* Placeholder for additional sections */}\n      <div className=\"h-screen bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold text-white mb-4\">More sections coming soon...</h2>\n          <p className=\"text-gray-400\">Problem/Solution, Features, Testimonials, Pricing</p>\n        </div>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;QACnB;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,wIAAA,CAAA,UAAI;;;;;0BAGL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAKvC;GAnBwB;KAAA", "debugId": null}}]}