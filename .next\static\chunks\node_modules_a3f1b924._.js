(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/vanta/src/_base.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_vanta_src_c6624f1d._.js",
  "static/chunks/node_modules_vanta_src__base_60a47121.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/vanta/src/_base.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/three/build/three.module.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_three_build_three_module_90b4155f.js",
  "static/chunks/node_modules_three_build_three_module_6f6b6dc6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/three/build/three.module.js [app-client] (ecmascript)");
    });
});
}}),
}]);