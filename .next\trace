[{"name": "hot-reloader", "duration": 72, "timestamp": 39670984957, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748447302205, "traceId": "86aa1d7f0ee969d4"}, {"name": "setup-dev-bundler", "duration": 1388815, "timestamp": 39669944833, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748447301165, "traceId": "86aa1d7f0ee969d4"}, {"name": "run-instrumentation-hook", "duration": 17, "timestamp": 39671366331, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748447302587, "traceId": "86aa1d7f0ee969d4"}, {"name": "start-dev-server", "duration": 1806932, "timestamp": 39669572131, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "50346643456", "memory.totalMem": "68560273408", "memory.heapSizeLimit": "34481373184", "memory.rss": "168374272", "memory.heapTotal": "112168960", "memory.heapUsed": "57800048"}, "startTime": 1748447300793, "traceId": "86aa1d7f0ee969d4"}, {"name": "compile-path", "duration": 3162288, "timestamp": 39695437633, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748447326658, "traceId": "86aa1d7f0ee969d4"}, {"name": "ensure-page", "duration": 3163478, "timestamp": 39695436832, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748447326657, "traceId": "86aa1d7f0ee969d4"}]