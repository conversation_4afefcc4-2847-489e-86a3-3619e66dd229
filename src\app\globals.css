@import "tailwindcss";

/* Custom CSS Variables for ReportU Theme */
:root {
  /* Primary Colors */
  --primary-blue: #3b82f6;
  --primary-purple: #8b5cf6;
  --primary-cyan: #06b6d4;

  /* Background Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-muted: #9ca3af;

  /* Accent Colors */
  --accent-green: #10b981;
  --accent-red: #ef4444;
  --accent-yellow: #f59e0b;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
  --gradient-secondary: linear-gradient(135deg, var(--primary-cyan), var(--primary-blue));
  --gradient-accent: linear-gradient(135deg, var(--primary-purple), var(--accent-green));

  /* Shadows */
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
  --shadow-glow-cyan: 0 0 20px rgba(6, 182, 212, 0.3);

  /* Animation Durations */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
}

@theme inline {
  /* Custom Font Families */
  --font-inter: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Custom Colors */
  --color-primary-blue: var(--primary-blue);
  --color-primary-purple: var(--primary-purple);
  --color-primary-cyan: var(--primary-cyan);
  --color-bg-primary: var(--bg-primary);
  --color-bg-secondary: var(--bg-secondary);
  --color-bg-tertiary: var(--bg-tertiary);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);
  --color-accent-green: var(--accent-green);
  --color-accent-red: var(--accent-red);
  --color-accent-yellow: var(--accent-yellow);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-inter);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-purple);
}

/* Selection Styles */
::selection {
  background: var(--primary-blue);
  color: white;
}

::-moz-selection {
  background: var(--primary-blue);
  color: white;
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Utility Classes */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-bg {
  background: var(--gradient-primary);
}

.glow-blue {
  box-shadow: var(--shadow-glow);
}

.glow-purple {
  box-shadow: var(--shadow-glow-purple);
}

.glow-cyan {
  box-shadow: var(--shadow-glow-cyan);
}

/* Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

/* Keyframe Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Typography */
.text-responsive-xl {
  font-size: clamp(2rem, 5vw, 4rem);
}

.text-responsive-lg {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

.text-responsive-md {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
}

/* Button Styles */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--primary-blue);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

/* Card Styles */
.card {
  background: var(--bg-secondary);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all var(--duration-normal) ease;
}

.card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-glow);
}

/* Glass Effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading Animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}
