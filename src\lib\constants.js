// ReportU Application Constants

// Application Information
export const APP_INFO = {
  name: 'ReportU',
  tagline: 'Cross-Border Offense Reporting Platform',
  description: 'Report offenses across Malaysia & Singapore with multimedia evidence. Automatically routed to appropriate authorities with real-time tracking.',
  version: '1.0.0',
  author: 'ReportU Team',
  website: 'https://reportu.vercel.app',
  logo: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp'
};

// Countries and Authorities
export const COUNTRIES = {
  MALAYSIA: 'malaysia',
  SINGAPORE: 'singapore'
};

export const AUTHORITIES = {
  // Malaysia
  JPJ: {
    id: 'jpj',
    name: '<PERSON><PERSON> (Road Transport Department)',
    country: COUNTRIES.MALAYSIA,
    types: ['traffic', 'vehicle'],
    contact: '+603-8888-1000',
    website: 'https://www.jpj.gov.my'
  },
  PDRM: {
    id: 'pdrm',
    name: '<PERSON>R<PERSON> (Royal Malaysia Police)',
    country: COUNTRIES.MALAYSIA,
    types: ['crime', 'public', 'emergency'],
    contact: '999',
    website: 'https://www.rmp.gov.my'
  },
  KPDNHEP: {
    id: 'kpdnhep',
    name: 'KPDNHEP (Consumer Protection)',
    country: COUNTRIES.MALAYSIA,
    types: ['consumer', 'counterfeit'],
    contact: '+603-8882-5555',
    website: 'https://www.kpdnhep.gov.my'
  },
  
  // Singapore
  LTA: {
    id: 'lta',
    name: 'LTA (Land Transport Authority)',
    country: COUNTRIES.SINGAPORE,
    types: ['traffic', 'vehicle'],
    contact: '+65-6225-5582',
    website: 'https://www.lta.gov.sg'
  },
  SPF: {
    id: 'spf',
    name: 'SPF (Singapore Police Force)',
    country: COUNTRIES.SINGAPORE,
    types: ['crime', 'public', 'emergency'],
    contact: '999',
    website: 'https://www.police.gov.sg'
  },
  CASE: {
    id: 'case',
    name: 'CASE (Consumer Protection)',
    country: COUNTRIES.SINGAPORE,
    types: ['consumer', 'counterfeit'],
    contact: '+65-6100-0315',
    website: 'https://www.case.org.sg'
  }
};

// Offense Types
export const OFFENSE_TYPES = {
  TRAFFIC: {
    id: 'traffic',
    name: 'Traffic Violations',
    icon: '🚗',
    description: 'Speeding, illegal parking, running red lights, reckless driving',
    examples: ['Speeding', 'Illegal Parking', 'Red Light Violation', 'Reckless Driving', 'Lane Cutting'],
    authorities: {
      [COUNTRIES.MALAYSIA]: ['jpj', 'pdrm'],
      [COUNTRIES.SINGAPORE]: ['lta', 'spf']
    }
  },
  PUBLIC: {
    id: 'public',
    name: 'Public Disturbances',
    icon: '🏛️',
    description: 'Noise complaints, littering, vandalism, public safety issues',
    examples: ['Noise Complaint', 'Littering', 'Vandalism', 'Illegal Dumping', 'Public Safety'],
    authorities: {
      [COUNTRIES.MALAYSIA]: ['pdrm'],
      [COUNTRIES.SINGAPORE]: ['spf']
    }
  },
  CONSUMER: {
    id: 'consumer',
    name: 'Consumer Protection',
    icon: '🛡️',
    description: 'Counterfeit goods, false advertising, unfair practices',
    examples: ['Counterfeit Products', 'False Advertising', 'Unfair Pricing', 'Poor Service', 'Scams'],
    authorities: {
      [COUNTRIES.MALAYSIA]: ['kpdnhep'],
      [COUNTRIES.SINGAPORE]: ['case']
    }
  },
  CRIME: {
    id: 'crime',
    name: 'Criminal Activities',
    icon: '⚖️',
    description: 'Theft, assault, fraud, drug-related offenses',
    examples: ['Theft', 'Assault', 'Fraud', 'Drug Activity', 'Harassment'],
    authorities: {
      [COUNTRIES.MALAYSIA]: ['pdrm'],
      [COUNTRIES.SINGAPORE]: ['spf']
    }
  },
  OTHER: {
    id: 'other',
    name: 'Other Offenses',
    icon: '📋',
    description: 'Other violations not covered in specific categories',
    examples: ['Environmental Issues', 'Building Violations', 'Health Code Violations'],
    authorities: {
      [COUNTRIES.MALAYSIA]: ['pdrm'],
      [COUNTRIES.SINGAPORE]: ['spf']
    }
  }
};

// Report Status
export const REPORT_STATUS = {
  SUBMITTED: {
    id: 'submitted',
    name: 'Submitted',
    description: 'Report has been submitted and is being processed',
    color: '#f59e0b',
    icon: '📝'
  },
  PROCESSING: {
    id: 'processing',
    name: 'Processing',
    description: 'Report is being reviewed by the appropriate authority',
    color: '#3b82f6',
    icon: '⏳'
  },
  INVESTIGATING: {
    id: 'investigating',
    name: 'Investigating',
    description: 'Authority is actively investigating the reported offense',
    color: '#8b5cf6',
    icon: '🔍'
  },
  RESOLVED: {
    id: 'resolved',
    name: 'Resolved',
    description: 'Report has been resolved and action has been taken',
    color: '#10b981',
    icon: '✅'
  },
  CLOSED: {
    id: 'closed',
    name: 'Closed',
    description: 'Report has been closed (no action required or insufficient evidence)',
    color: '#6b7280',
    icon: '❌'
  }
};

// Evidence Types
export const EVIDENCE_TYPES = {
  IMAGE: {
    id: 'image',
    name: 'Photo',
    extensions: ['.jpg', '.jpeg', '.png', '.webp'],
    maxSize: 10 * 1024 * 1024, // 10MB
    icon: '📸'
  },
  VIDEO: {
    id: 'video',
    name: 'Video',
    extensions: ['.mp4', '.mov', '.avi', '.webm'],
    maxSize: 50 * 1024 * 1024, // 50MB
    icon: '🎥'
  },
  AUDIO: {
    id: 'audio',
    name: 'Audio',
    extensions: ['.mp3', '.wav', '.m4a', '.ogg'],
    maxSize: 20 * 1024 * 1024, // 20MB
    icon: '🎵'
  }
};

// Languages
export const LANGUAGES = {
  EN: {
    code: 'en',
    name: 'English',
    flag: '🇺🇸'
  },
  MS: {
    code: 'ms',
    name: 'Bahasa Malaysia',
    flag: '🇲🇾'
  },
  ZH: {
    code: 'zh',
    name: '中文',
    flag: '🇨🇳'
  },
  TA: {
    code: 'ta',
    name: 'தமிழ்',
    flag: '🇮🇳'
  }
};

// Navigation Links
export const NAV_LINKS = [
  { href: '/', label: 'Home', icon: '🏠' },
  { href: '/demo', label: 'Demo', icon: '🎮' },
  { href: '/report', label: 'Report', icon: '📝' },
  { href: '/dashboard', label: 'Dashboard', icon: '📊' },
  { href: '/about', label: 'About', icon: 'ℹ️' }
];

// Social Links
export const SOCIAL_LINKS = {
  twitter: 'https://twitter.com/reportu',
  facebook: 'https://facebook.com/reportu',
  linkedin: 'https://linkedin.com/company/reportu',
  github: 'https://github.com/reportu'
};

// API Endpoints (Simulated)
export const API_ENDPOINTS = {
  REPORTS: '/api/reports',
  UPLOAD: '/api/upload',
  AUTHORITIES: '/api/authorities',
  STATISTICS: '/api/statistics'
};

// Animation Configurations
export const ANIMATIONS = {
  DURATION: {
    FAST: 0.2,
    NORMAL: 0.3,
    SLOW: 0.5,
    EXTRA_SLOW: 1.0
  },
  EASING: {
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
  }
};

// Breakpoints
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
};

// Feature Flags
export const FEATURES = {
  ANONYMOUS_REPORTING: true,
  MULTI_LANGUAGE: true,
  REAL_TIME_TRACKING: true,
  COMMUNITY_DASHBOARD: true,
  CROSS_BORDER: true,
  AI_CATEGORIZATION: true
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  INVALID_FILE_TYPE: 'Invalid file type. Please select a supported file.',
  LOCATION_ERROR: 'Unable to detect your location. Please enable location services.',
  SUBMISSION_ERROR: 'Failed to submit report. Please try again later.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  REPORT_SUBMITTED: 'Report submitted successfully! You will receive updates on the progress.',
  FILE_UPLOADED: 'File uploaded successfully.',
  LOCATION_DETECTED: 'Location detected successfully.'
};

export default {
  APP_INFO,
  COUNTRIES,
  AUTHORITIES,
  OFFENSE_TYPES,
  REPORT_STATUS,
  EVIDENCE_TYPES,
  LANGUAGES,
  NAV_LINKS,
  SOCIAL_LINKS,
  API_ENDPOINTS,
  ANIMATIONS,
  BREAKPOINTS,
  FEATURES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
};
